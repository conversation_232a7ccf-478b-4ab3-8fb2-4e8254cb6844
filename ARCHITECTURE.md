# 🏗️ Project Architecture

This document outlines the architecture and organization of the project, specifically focusing on the separation between reusable components and application-specific code.

## 📁 **Directory Structure**

```
src/
├── mui-components/          # 🎨 REUSABLE MUI COMPONENT LIBRARY
│   ├── inputs/             # Input components (buttons, forms, etc.)
│   ├── data-display/       # Data display components (tables, lists, etc.)
│   ├── feedback/           # Feedback components (alerts, progress, etc.)
│   ├── surface/            # Surface components (cards, papers, etc.)
│   ├── navigation/         # Navigation components (breadcrumbs, tabs, etc.)
│   ├── layout/             # Layout components (grids, containers, etc.)
│   ├── lab/                # Experimental components (date pickers, etc.)
│   ├── index.ts           # Library exports
│   └── README.md          # Library documentation
│
├── components/             # 🏗️ APPLICATION-SPECIFIC COMPONENTS
│   ├── AnalysisResults.tsx # Business logic components
│   ├── ConfigurationPanel.tsx
│   ├── CodeGenerationPanel.tsx
│   ├── ui/                # shadcn/ui design system components
│   ├── index.ts          # App component exports
│   └── README.md         # App component documentation
│
├── pages/                 # 📄 APPLICATION PAGES
├── lib/                   # 🛠️ UTILITIES AND HELPERS
├── hooks/                 # 🪝 CUSTOM REACT HOOKS
└── types/                 # 📝 TYPESCRIPT DEFINITIONS
```

## 🎯 **Design Principles**

### **1. Separation of Concerns**
- **MUI Components**: Reusable, framework-agnostic UI components
- **App Components**: Business logic and application-specific functionality
- **Pages**: Route-level components that compose other components

### **2. Import Boundaries**
```typescript
// ✅ ALLOWED IMPORTS
src/mui-components/     → @mui/material, React, utilities
src/components/         → @/mui-components, @/lib, @/hooks, @/types
src/pages/             → @/components, @/mui-components, @/lib
src/lib/               → External libraries, utilities
src/hooks/             → React, @/lib, @/types

// ❌ FORBIDDEN IMPORTS
src/mui-components/     ❌ @/components (app-specific code)
src/mui-components/     ❌ @/pages (application pages)
src/mui-components/     ❌ @/hooks (application hooks)
```

### **3. Component Categories**

#### **🎨 MUI Components (`src/mui-components/`)**
- **Purpose**: Reusable UI components that wrap Material-UI
- **Scope**: Framework-agnostic, no business logic
- **Examples**: MuiButton, MuiTable, MuiDialog
- **Storybook**: ✅ Documented in Storybook
- **Testing**: Unit tests for UI behavior

#### **🏗️ Application Components (`src/components/`)**
- **Purpose**: Business logic and app-specific functionality
- **Scope**: Application-specific, can use MUI components
- **Examples**: AnalysisResults, ConfigurationPanel
- **Storybook**: ❌ Not in Storybook (app-specific)
- **Testing**: Integration tests for business logic

#### **🎨 UI Components (`src/components/ui/`)**
- **Purpose**: shadcn/ui design system components
- **Scope**: Reusable within the application
- **Examples**: Button, Card, Input
- **Storybook**: ⚠️ Optional (design system components)
- **Testing**: Visual regression tests

## 🔄 **Data Flow**

```
Pages → App Components → MUI Components → Material-UI
  ↓         ↓              ↓
Hooks → Utilities → External APIs
```

## 📦 **Import Patterns**

### **In Pages**
```typescript
// Pages can import from anywhere
import { AnalysisResults } from '@/components';
import { MuiContainer, MuiStack } from '@/mui-components';
import { useAnalysis } from '@/hooks';
import { Button } from '@/components/ui/button';
```

### **In Application Components**
```typescript
// App components can use MUI components and utilities
import { MuiButton, MuiDialog } from '@/mui-components';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
```

### **In MUI Components**
```typescript
// MUI components should only import from Material-UI and React
import { Button, ButtonProps } from '@mui/material';
import { forwardRef } from 'react';
// ❌ NO imports from @/components, @/hooks, @/lib
```

## 🧪 **Testing Strategy**

### **MUI Components**
- **Unit Tests**: Component rendering, props, interactions
- **Visual Tests**: Storybook visual regression testing
- **Accessibility Tests**: ARIA compliance, keyboard navigation

### **Application Components**
- **Integration Tests**: Component interactions with hooks/APIs
- **Business Logic Tests**: Data processing, state management
- **User Flow Tests**: End-to-end scenarios

### **Pages**
- **E2E Tests**: Full user journeys
- **Performance Tests**: Page load times, bundle size
- **SEO Tests**: Meta tags, accessibility

## 🚀 **Development Workflow**

### **Adding New MUI Components**
1. Create component in appropriate `src/mui-components/` category
2. Add TypeScript types and JSDoc documentation
3. Create comprehensive Storybook stories
4. Add unit tests for component behavior
5. Export from `src/mui-components/index.ts`
6. Update documentation

### **Adding New Application Components**
1. Create component in `src/components/`
2. Import required MUI components and utilities
3. Add TypeScript types and business logic
4. Create integration tests
5. Export from `src/components/index.ts`
6. Update documentation

## 🔧 **Configuration**

### **Storybook**
- **Scans**: Only `src/mui-components/**/*.stories.tsx`
- **Purpose**: Document reusable component library
- **URL**: http://localhost:6006/

### **Path Aliases**
```typescript
// vite.config.ts & tsconfig.json
{
  "@/*": ["./src/*"],
  "@/mui-components/*": ["./src/mui-components/*"]
}
```

### **Build Process**
- **Development**: Vite dev server with hot reload
- **Production**: Optimized bundle with code splitting
- **Storybook**: Separate build for component documentation

## 📋 **Best Practices**

1. **Keep boundaries clear** - Don't mix reusable and app-specific code
2. **Use TypeScript** - Ensure type safety across all components
3. **Document everything** - README files and JSDoc comments
4. **Test thoroughly** - Unit, integration, and E2E tests
5. **Follow conventions** - Consistent naming and file organization
6. **Review imports** - Ensure proper separation of concerns
7. **Update documentation** - Keep architecture docs current

## 🔍 **Monitoring & Maintenance**

- **Bundle Analysis**: Monitor component library size
- **Dependency Updates**: Keep MUI and other deps current
- **Performance Monitoring**: Track component render performance
- **Accessibility Audits**: Regular a11y compliance checks
- **Code Reviews**: Enforce architectural boundaries
