# 🏗️ Application Components

This directory contains **application-specific components** that are tailored to the specific needs of this application.

> ⚠️ **Important**: These components are **application-specific** and may import from both the MUI component library and other application modules.

## 🏗️ **Architecture**

```
src/components/             # APPLICATION-SPECIFIC COMPONENTS
├── AnalysisResults.tsx    # Analysis results display component
├── CodeGenerationPanel.tsx # Code generation interface
├── ConfigurationPanel.tsx # Configuration management
├── ui/                    # shadcn/ui components (design system)
├── index.ts              # Application component exports
└── README.md             # This file

src/mui-components/        # REUSABLE MUI LIBRARY (separate)
├── inputs/               # Reusable input components
├── data-display/         # Reusable data display components
└── ...                   # Other reusable categories
```

## 📋 **Component Categories**

### **Application Components**
- **AnalysisResults.tsx** - Displays analysis results and insights
- **CodeGenerationPanel.tsx** - Interface for code generation features
- **ConfigurationPanel.tsx** - Application configuration management

### **UI Components (shadcn/ui)**
The `ui/` directory contains shadcn/ui components that provide a consistent design system:
- Form components (input, button, select, etc.)
- Layout components (card, separator, etc.)
- Feedback components (toast, alert, etc.)
- Navigation components (tabs, pagination, etc.)

## 🚀 **Usage**

### **Import Guidelines**

✅ **DO**: Import application components from `@/components`  
✅ **DO**: Import MUI components from `@/mui-components`  
✅ **DO**: Import shadcn/ui components from `@/components/ui`  
❌ **DON'T**: Import application components in MUI library components  

```typescript
// ✅ CORRECT: Import application-specific components
import { AnalysisResults, ConfigurationPanel } from '@/components';

// ✅ CORRECT: Import shadcn/ui components
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

// ✅ CORRECT: Import MUI components for reusable functionality
import { MuiButton, MuiCard } from '@/mui-components';
```

### **Example Usage**

```typescript
import { AnalysisResults } from '@/components';
import { MuiContainer, MuiStack } from '@/mui-components';
import { Card } from '@/components/ui/card';

function Dashboard() {
  return (
    <MuiContainer maxWidth="lg">
      <MuiStack spacing={3}>
        <Card>
          <AnalysisResults data={analysisData} />
        </Card>
      </MuiStack>
    </MuiContainer>
  );
}
```

## 🎯 **Development Guidelines**

### **When to Create Components Here**
- Components specific to this application's business logic
- Components that combine multiple MUI components for app-specific use cases
- Components that handle application state or API calls
- Custom UI components that extend shadcn/ui components

### **When to Use MUI Components Instead**
- Generic, reusable UI components
- Components that could be used in other applications
- Pure UI components without business logic
- Components that wrap Material-UI functionality

## 🔄 **Component Communication**

```typescript
// Application components can import from MUI library
import { MuiButton, MuiDialog } from '@/mui-components';

// Application components can use shadcn/ui
import { Button } from '@/components/ui/button';

// Application components can import other app components
import { ConfigurationPanel } from '@/components/ConfigurationPanel';
```

## 📝 **Best Practices**

1. **Keep components focused** - Each component should have a single responsibility
2. **Use TypeScript** - All components should have proper type definitions
3. **Document props** - Use JSDoc comments for component props
4. **Handle errors gracefully** - Implement proper error boundaries and fallbacks
5. **Follow naming conventions** - Use PascalCase for component names
6. **Export from index.ts** - Add new components to the main index file

## 🧪 **Testing**

Application components should be tested with:
- Unit tests for component logic
- Integration tests for component interactions
- Visual regression tests for UI consistency

```typescript
// Example test structure
describe('AnalysisResults', () => {
  it('should render analysis data correctly', () => {
    // Test implementation
  });
  
  it('should handle empty data gracefully', () => {
    // Test implementation
  });
});
```
