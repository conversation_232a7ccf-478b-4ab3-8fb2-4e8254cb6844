import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CodebaseAnalysis } from '@/hooks/useCodebaseAnalyzer';
import { StorybookData } from '@/hooks/useStorybookParser';
import { Package, Palette, FileCode, Book } from 'lucide-react';

interface AnalysisResultsProps {
  codebaseAnalysis: CodebaseAnalysis;
  storybookData: StorybookData;
}

export const AnalysisResults = ({ codebaseAnalysis, storybookData }: AnalysisResultsProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          Analysis Results
        </CardTitle>
        <CardDescription>
          Discovered components, tokens, and patterns from your codebase
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="components" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="components">Components</TabsTrigger>
            <TabsTrigger value="tokens">Tokens</TabsTrigger>
            <TabsTrigger value="storybook">Storybook</TabsTrigger>
            <TabsTrigger value="conventions">Patterns</TabsTrigger>
          </TabsList>

          <TabsContent value="components" className="space-y-4">
            <div className="grid gap-3">
              {codebaseAnalysis.components.map((component, index) => (
                <div key={index} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <FileCode className="h-4 w-4" />
                      <span className="font-medium">{component.name}</span>
                    </div>
                    <Badge variant={component.category === 'atom' ? 'default' : 
                                 component.category === 'molecule' ? 'secondary' : 'outline'}>
                      {component.category}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">{component.path}</p>
                  <div className="flex flex-wrap gap-1">
                    {Object.keys(component.props).map((prop) => (
                      <Badge key={prop} variant="outline" className="text-xs">
                        {prop}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="tokens" className="space-y-4">
            <div className="grid gap-3">
              {codebaseAnalysis.tokens.map((token, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Palette className="h-4 w-4" />
                    <div>
                      <div className="font-medium">{token.name}</div>
                      <div className="text-sm text-muted-foreground">{token.value}</div>
                    </div>
                  </div>
                  <Badge variant="outline">{token.category}</Badge>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="storybook" className="space-y-4">
            <div className="grid gap-3">
              {storybookData.stories.map((story, index) => (
                <div key={index} className="p-3 border rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Book className="h-4 w-4" />
                    <span className="font-medium">{story.component}</span>
                    <Badge variant="secondary">{story.name}</Badge>
                  </div>
                  <div className="text-sm text-muted-foreground mb-2">
                    Args: {Object.keys(story.args).join(', ')}
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {Object.keys(story.argTypes).map((arg) => (
                      <Badge key={arg} variant="outline" className="text-xs">
                        {arg}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="conventions" className="space-y-4">
            <div className="space-y-3">
              <div className="p-3 border rounded-lg">
                <div className="font-medium mb-1">Component Style</div>
                <Badge>{codebaseAnalysis.conventions.componentStyle}</Badge>
              </div>
              <div className="p-3 border rounded-lg">
                <div className="font-medium mb-1">Styling Approach</div>
                <Badge>{codebaseAnalysis.conventions.stylingApproach}</Badge>
              </div>
              <div className="p-3 border rounded-lg">
                <div className="font-medium mb-1">Import Pattern</div>
                <code className="text-sm bg-muted px-2 py-1 rounded">
                  {codebaseAnalysis.conventions.importStyle}
                </code>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};