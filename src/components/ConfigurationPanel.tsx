import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Key, Globe, Code2 } from 'lucide-react';

interface ConfigurationPanelProps {
  onAnalyze: (config: ConfigurationData) => void;
  isLoading: boolean;
}

export interface ConfigurationData {
  figmaApiKey: string;
  storybookUrl: string;
  targetFramework: 'react' | 'vue' | 'angular';
  projectPath?: string;
}

export const ConfigurationPanel = ({ onAnalyze, isLoading }: ConfigurationPanelProps) => {
  // Mock existing API keys - in a real app, these would come from a secure storage/database
  const existingApiKeys = [
    { id: '1', name: 'Production Key', key: 'fig_prod_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx', description: 'Main production environment' },
    { id: '2', name: 'Development Key', key: 'fig_dev_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx', description: 'Development environment' },
    { id: '3', name: 'Testing Key', key: 'fig_test_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx', description: 'Testing and staging' },
    { id: '4', name: 'Personal Key', key: 'fig_personal_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx', description: 'Personal projects' }
  ];

  const [config, setConfig] = useState<ConfigurationData>({
    figmaApiKey: '',
    storybookUrl: '',
    targetFramework: 'react'
  });

  const handleSubmit = () => {
    if (config.figmaApiKey && config.storybookUrl) {
      onAnalyze(config);
    }
  };

  const isValid = config.figmaApiKey && config.storybookUrl;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Code2 className="h-5 w-5" />
          Configuration
        </CardTitle>
        <CardDescription>
          Set up your development environment for intelligent code generation
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="figma-key" className="flex items-center gap-2">
            <Key className="h-4 w-4" />
            Figma API Key
          </Label>
          <Select
            value={config.figmaApiKey}
            onValueChange={(value) => setConfig(prev => ({ ...prev, figmaApiKey: value }))}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select an API key" />
            </SelectTrigger>
            <SelectContent>
              {existingApiKeys.map((apiKey) => (
                <SelectItem key={apiKey.id} value={apiKey.key}>
                  <div className="flex flex-col">
                    <span className="font-medium">{apiKey.name}</span>
                    <span className="text-xs text-muted-foreground">{apiKey.description}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="storybook-url" className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            Storybook URL
          </Label>
          <Input
            id="storybook-url"
            placeholder="https://your-storybook.com"
            value={config.storybookUrl}
            onChange={(e) => setConfig(prev => ({ ...prev, storybookUrl: e.target.value }))}
          />
        </div>

        <div className="space-y-2">
          <Label>Target Framework</Label>
          <Select 
            value={config.targetFramework} 
            onValueChange={(value: 'react' | 'vue' | 'angular') => 
              setConfig(prev => ({ ...prev, targetFramework: value }))
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="react">React</SelectItem>
              <SelectItem value="vue">Vue</SelectItem>
              <SelectItem value="angular">Angular</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex gap-2">
          <Badge variant="secondary">Atomic Design</Badge>
          <Badge variant="secondary">Token Mapping</Badge>
          <Badge variant="secondary">Auto Documentation</Badge>
        </div>

        <Button 
          onClick={handleSubmit} 
          disabled={!isValid || isLoading}
          className="w-full"
        >
          {isLoading ? 'Analyzing...' : 'Start Analysis'}
        </Button>
      </CardContent>
    </Card>
  );
};