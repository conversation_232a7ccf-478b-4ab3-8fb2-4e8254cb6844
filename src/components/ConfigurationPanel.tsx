import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Globe, Code2 } from 'lucide-react';

interface ConfigurationPanelProps {
  onAnalyze: (config: ConfigurationData) => void;
  isLoading: boolean;
}

export interface ConfigurationData {
  figmaApiKey: string;
  storybookUrl: string;
  targetFramework: 'react' | 'vue' | 'angular';
  projectPath?: string;
}

export const ConfigurationPanel = ({ onAnalyze, isLoading }: ConfigurationPanelProps) => {
  const [config, setConfig] = useState<ConfigurationData>({
    figmaApiKey: '',
    storybookUrl: '',
    targetFramework: 'react'
  });

  const handleSubmit = () => {
    if (config.storybookUrl) {
      onAnalyze(config);
    }
  };

  const isValid = config.storybookUrl;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Code2 className="h-5 w-5" />
          Configuration
        </CardTitle>
        <CardDescription>
          Set up your development environment for intelligent code generation
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="storybook-url" className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            Storybook URL
          </Label>
          <Input
            id="storybook-url"
            placeholder="https://your-storybook.com"
            value={config.storybookUrl}
            onChange={(e) => setConfig(prev => ({ ...prev, storybookUrl: e.target.value }))}
          />
        </div>

        <div className="space-y-2">
          <Label>Target Framework</Label>
          <Select 
            value={config.targetFramework} 
            onValueChange={(value: 'react' | 'vue' | 'angular') => 
              setConfig(prev => ({ ...prev, targetFramework: value }))
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="react">React</SelectItem>
              <SelectItem value="vue">Vue</SelectItem>
              <SelectItem value="angular">Angular</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex gap-2">
          <Badge variant="secondary">Atomic Design</Badge>
          <Badge variant="secondary">Token Mapping</Badge>
          <Badge variant="secondary">Auto Documentation</Badge>
        </div>

        <Button 
          onClick={handleSubmit} 
          disabled={!isValid || isLoading}
          className="w-full"
        >
          {isLoading ? 'Analyzing...' : 'Start Analysis'}
        </Button>
      </CardContent>
    </Card>
  );
};