import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { GeneratedCode } from '@/hooks/useCodeGenerator';
import { Badge } from '@/components/ui/badge';
import { Copy, Wand2, FileCode, Book, Link, Key } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';

interface CodeGenerationPanelProps {
  onGenerate: (figmaUrl: string, atomicLevel: string) => void;
  generatedCode: GeneratedCode | null;
  isGenerating: boolean;
}

export const CodeGenerationPanel = ({ onGenerate, generatedCode, isGenerating }: CodeGenerationPanelProps) => {
  // Mock existing API keys - in a real app, these would come from a secure storage/database
  const existingApiKeys = [
    { id: '1', name: 'Production Key', key: 'fig_prod_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx', description: 'Main production environment' },
    { id: '2', name: 'Development Key', key: 'fig_dev_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx', description: 'Development environment' },
    { id: '3', name: 'Testing Key', key: 'fig_test_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx', description: 'Testing and staging' },
    { id: '4', name: 'Personal Key', key: 'fig_personal_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx', description: 'Personal projects' }
  ];

  const [figmaApiKey, setFigmaApiKey] = useState('');
  const [figmaUrl, setFigmaUrl] = useState('');
  const [atomicLevel, setAtomicLevel] = useState('molecule');
  const { toast } = useToast();

  const handleGenerate = () => {
    if (figmaApiKey && figmaUrl.trim() && isValidFigmaUrl(figmaUrl)) {
      onGenerate(figmaUrl, atomicLevel);
    }
  };

  const isValidFigmaUrl = (url: string) => {
    return url.includes('figma.com') && url.includes('node-id');
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied!",
      description: "Code copied to clipboard",
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Link className="h-5 w-5" />
            Figma Frame Analysis
          </CardTitle>
          <CardDescription>
            Paste a Figma frame URL and we'll analyze it to generate production-ready code
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="figma-key" className="flex items-center gap-2">
              <Key className="h-4 w-4" />
              Figma API Key
            </Label>
            <Select
              value={figmaApiKey}
              onValueChange={setFigmaApiKey}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select an API key" />
              </SelectTrigger>
              <SelectContent>
                {existingApiKeys.map((apiKey) => (
                  <SelectItem key={apiKey.id} value={apiKey.key}>
                    <div className="flex flex-col">
                      <span className="font-medium">{apiKey.name}</span>
                      <span className="text-xs text-muted-foreground">{apiKey.description}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="figma-url">Figma Frame URL</Label>
            <Input
              id="figma-url"
              placeholder="https://www.figma.com/file/abc123/Design-System?node-id=1%3A2"
              value={figmaUrl}
              onChange={(e) => setFigmaUrl(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label>Atomic Design Level</Label>
            <Select value={atomicLevel} onValueChange={setAtomicLevel}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="atom">Atom (Basic elements)</SelectItem>
                <SelectItem value="molecule">Molecule (Simple components)</SelectItem>
                <SelectItem value="organism">Organism (Complex components)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button
            onClick={handleGenerate}
            disabled={!figmaApiKey || !figmaUrl.trim() || !isValidFigmaUrl(figmaUrl) || isGenerating}
            className="w-full"
          >
            {isGenerating ? 'Analyzing Frame...' : 'Analyze & Generate'}
          </Button>
        </CardContent>
      </Card>

      {generatedCode && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileCode className="h-5 w-5" />
              Generated Code
            </CardTitle>
            <CardDescription>
              Production-ready {generatedCode.componentName} component
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="component" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="component">Component</TabsTrigger>
                <TabsTrigger value="imports">Imports</TabsTrigger>
                <TabsTrigger value="story">Storybook</TabsTrigger>
              </TabsList>

              <TabsContent value="component" className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex gap-2">
                    {generatedCode.dependencies.map((dep, index) => (
                      <Badge key={index} variant="outline">{dep.split('/').pop()}</Badge>
                    ))}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(generatedCode.code)}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </Button>
                </div>
                <div className="relative">
                  <pre className="bg-muted p-4 rounded-lg overflow-x-auto text-sm">
                    <code>{generatedCode.code}</code>
                  </pre>
                </div>
              </TabsContent>

              <TabsContent value="imports" className="space-y-4">
                <div className="flex justify-end">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(generatedCode.imports.join('\n'))}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </Button>
                </div>
                <div className="relative">
                  <pre className="bg-muted p-4 rounded-lg overflow-x-auto text-sm">
                    <code>{generatedCode.imports.join('\n')}</code>
                  </pre>
                </div>
              </TabsContent>

              <TabsContent value="story" className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Book className="h-4 w-4" />
                    <span className="text-sm text-muted-foreground">Auto-generated Storybook story</span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(generatedCode.storyCode || '')}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </Button>
                </div>
                <div className="relative">
                  <pre className="bg-muted p-4 rounded-lg overflow-x-auto text-sm">
                    <code>{generatedCode.storyCode}</code>
                  </pre>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  );
};