import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { GeneratedCode } from '@/hooks/useCodeGenerator';
import { Badge } from '@/components/ui/badge';
import { Copy, Wand2, FileCode, Book, Link } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';

interface CodeGenerationPanelProps {
  onGenerate: (figmaUrl: string, atomicLevel: string) => void;
  generatedCode: GeneratedCode | null;
  isGenerating: boolean;
}

export const CodeGenerationPanel = ({ onGenerate, generatedCode, isGenerating }: CodeGenerationPanelProps) => {
  const [figmaUrl, setFigmaUrl] = useState('');
  const [atomicLevel, setAtomicLevel] = useState('molecule');
  const { toast } = useToast();

  const handleGenerate = () => {
    if (figmaUrl.trim() && isValidFigmaUrl(figmaUrl)) {
      onGenerate(figmaUrl, atomicLevel);
    }
  };

  const isValidFigmaUrl = (url: string) => {
    return url.includes('figma.com') && url.includes('node-id');
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied!",
      description: "Code copied to clipboard",
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Link className="h-5 w-5" />
            Figma Frame Analysis
          </CardTitle>
          <CardDescription>
            Paste a Figma frame URL and we'll analyze it to generate production-ready code
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="figma-url">Figma Frame URL</Label>
            <Input
              id="figma-url"
              placeholder="https://www.figma.com/file/abc123/Design-System?node-id=1%3A2"
              value={figmaUrl}
              onChange={(e) => setFigmaUrl(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label>Atomic Design Level</Label>
            <Select value={atomicLevel} onValueChange={setAtomicLevel}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="atom">Atom (Basic elements)</SelectItem>
                <SelectItem value="molecule">Molecule (Simple components)</SelectItem>
                <SelectItem value="organism">Organism (Complex components)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button 
            onClick={handleGenerate} 
            disabled={!figmaUrl.trim() || !isValidFigmaUrl(figmaUrl) || isGenerating}
            className="w-full"
          >
            {isGenerating ? 'Analyzing Frame...' : 'Analyze & Generate'}
          </Button>
        </CardContent>
      </Card>

      {generatedCode && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileCode className="h-5 w-5" />
              Generated Code
            </CardTitle>
            <CardDescription>
              Production-ready {generatedCode.componentName} component
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="component" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="component">Component</TabsTrigger>
                <TabsTrigger value="imports">Imports</TabsTrigger>
                <TabsTrigger value="story">Storybook</TabsTrigger>
              </TabsList>

              <TabsContent value="component" className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex gap-2">
                    {generatedCode.dependencies.map((dep, index) => (
                      <Badge key={index} variant="outline">{dep.split('/').pop()}</Badge>
                    ))}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(generatedCode.code)}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </Button>
                </div>
                <div className="relative">
                  <pre className="bg-muted p-4 rounded-lg overflow-x-auto text-sm">
                    <code>{generatedCode.code}</code>
                  </pre>
                </div>
              </TabsContent>

              <TabsContent value="imports" className="space-y-4">
                <div className="flex justify-end">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(generatedCode.imports.join('\n'))}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </Button>
                </div>
                <div className="relative">
                  <pre className="bg-muted p-4 rounded-lg overflow-x-auto text-sm">
                    <code>{generatedCode.imports.join('\n')}</code>
                  </pre>
                </div>
              </TabsContent>

              <TabsContent value="story" className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Book className="h-4 w-4" />
                    <span className="text-sm text-muted-foreground">Auto-generated Storybook story</span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(generatedCode.storyCode || '')}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </Button>
                </div>
                <div className="relative">
                  <pre className="bg-muted p-4 rounded-lg overflow-x-auto text-sm">
                    <code>{generatedCode.storyCode}</code>
                  </pre>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  );
};