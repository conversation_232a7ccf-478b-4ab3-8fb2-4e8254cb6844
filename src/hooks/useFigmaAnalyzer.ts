import { useState, useCallback } from 'react';

export interface FigmaFrame {
  id: string;
  name: string;
  type: string;
  children: FigmaNode[];
  fills?: FigmaFill[];
  effects?: FigmaEffect[];
}

export interface FigmaNode {
  id: string;
  name: string;
  type: string;
  children?: FigmaNode[];
  fills?: FigmaFill[];
  strokes?: FigmaStroke[];
  effects?: FigmaEffect[];
  characters?: string;
  style?: FigmaTextStyle;
  constraints?: FigmaConstraints;
  absoluteBoundingBox?: FigmaBoundingBox;
}

export interface FigmaFill {
  type: string;
  color?: FigmaColor;
  gradientStops?: FigmaGradientStop[];
}

export interface FigmaColor {
  r: number;
  g: number;
  b: number;
  a?: number;
}

export interface FigmaStroke {
  type: string;
  color: FigmaColor;
}

export interface FigmaEffect {
  type: string;
  color?: FigmaColor;
  offset?: { x: number; y: number };
  radius?: number;
}

export interface FigmaTextStyle {
  fontFamily: string;
  fontSize: number;
  fontWeight: number;
  textAlignHorizontal: string;
  textAlignVertical: string;
}

export interface FigmaConstraints {
  vertical: string;
  horizontal: string;
}

export interface FigmaBoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface FigmaGradientStop {
  position: number;
  color: FigmaColor;
}

export interface FigmaAnalysis {
  frame: FigmaFrame;
  extractedComponents: ExtractedComponent[];
  designTokens: ExtractedToken[];
  layout: LayoutInfo;
}

export interface ExtractedComponent {
  name: string;
  type: 'button' | 'input' | 'card' | 'text' | 'image' | 'container';
  properties: Record<string, any>;
  children?: ExtractedComponent[];
  position: { x: number; y: number; width: number; height: number };
}

export interface ExtractedToken {
  name: string;
  value: string;
  type: 'color' | 'spacing' | 'typography' | 'shadow' | 'border-radius';
}

export interface LayoutInfo {
  direction: 'row' | 'column';
  gap: number;
  padding: { top: number; right: number; bottom: number; left: number };
  alignment: string;
}

export const useFigmaAnalyzer = () => {
  const [analysis, setAnalysis] = useState<FigmaAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const analyzeFigmaFrame = useCallback(async (figmaUrl: string, apiKey: string) => {
    setIsAnalyzing(true);
    
    try {
      const { fileId, nodeId } = extractFigmaIds(figmaUrl);
      
      // Fetch frame data from Figma API
      const response = await fetch(`https://api.figma.com/v1/files/${fileId}/nodes?ids=${nodeId}`, {
        headers: {
          'X-Figma-Token': apiKey,
        },
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch Figma data');
      }
      
      const data = await response.json();
      const frameData = data.nodes[nodeId].document;
      
      // Analyze the frame and extract components
      const analysis: FigmaAnalysis = {
        frame: frameData,
        extractedComponents: extractComponents(frameData),
        designTokens: extractDesignTokens(frameData),
        layout: analyzeLayout(frameData)
      };
      
      setAnalysis(analysis);
    } catch (error) {
      console.error('Figma analysis failed:', error);
      throw error;
    } finally {
      setIsAnalyzing(false);
    }
  }, []);

  return {
    analysis,
    isAnalyzing,
    analyzeFigmaFrame
  };
};

function extractFigmaIds(url: string): { fileId: string; nodeId: string } {
  const fileMatch = url.match(/file\/([^\/]+)/);
  const nodeMatch = url.match(/node-id=([^&]+)/);
  
  if (!fileMatch || !nodeMatch) {
    throw new Error('Invalid Figma URL');
  }
  
  return {
    fileId: fileMatch[1],
    nodeId: decodeURIComponent(nodeMatch[1])
  };
}

function extractComponents(node: FigmaNode): ExtractedComponent[] {
  const components: ExtractedComponent[] = [];
  
  function traverse(node: FigmaNode) {
    // Detect component type based on Figma node properties
    const componentType = detectComponentType(node);
    
    if (componentType) {
      components.push({
        name: node.name,
        type: componentType,
        properties: extractProperties(node),
        position: node.absoluteBoundingBox || { x: 0, y: 0, width: 0, height: 0 },
        children: node.children ? node.children.map(child => ({
          name: child.name,
          type: detectComponentType(child) || 'container',
          properties: extractProperties(child),
          position: child.absoluteBoundingBox || { x: 0, y: 0, width: 0, height: 0 }
        })).filter(child => child.type !== 'container') : undefined
      });
    }
    
    if (node.children) {
      node.children.forEach(traverse);
    }
  }
  
  traverse(node);
  return components;
}

function detectComponentType(node: FigmaNode): ExtractedComponent['type'] | null {
  const name = node.name.toLowerCase();
  const type = node.type.toLowerCase();
  
  if (name.includes('button') || (type === 'instance' && name.includes('btn'))) return 'button';
  if (name.includes('input') || name.includes('textfield')) return 'input';
  if (name.includes('card') || (type === 'frame' && name.includes('card'))) return 'card';
  if (type === 'text') return 'text';
  if (name.includes('image') || name.includes('avatar') || name.includes('photo')) return 'image';
  if (type === 'frame' || type === 'group') return 'container';
  
  return null;
}

function extractProperties(node: FigmaNode): Record<string, any> {
  const props: Record<string, any> = {};
  
  if (node.fills && node.fills.length > 0) {
    const fill = node.fills[0];
    if (fill.color) {
      props.backgroundColor = rgbaToHsl(fill.color);
    }
  }
  
  if (node.strokes && node.strokes.length > 0) {
    const stroke = node.strokes[0];
    props.borderColor = rgbaToHsl(stroke.color);
  }
  
  if (node.effects) {
    const shadow = node.effects.find(effect => effect.type === 'DROP_SHADOW');
    if (shadow) {
      props.boxShadow = `${shadow.offset?.x || 0}px ${shadow.offset?.y || 0}px ${shadow.radius || 0}px ${rgbaToHsl(shadow.color!)}`;
    }
  }
  
  if (node.style) {
    props.fontSize = node.style.fontSize;
    props.fontWeight = node.style.fontWeight;
    props.fontFamily = node.style.fontFamily;
  }
  
  if (node.characters) {
    props.text = node.characters;
  }
  
  return props;
}

function extractDesignTokens(node: FigmaNode): ExtractedToken[] {
  const tokens: ExtractedToken[] = [];
  const colorMap = new Map<string, string>();
  
  function traverse(node: FigmaNode) {
    if (node.fills) {
      node.fills.forEach(fill => {
        if (fill.color) {
          const hsl = rgbaToHsl(fill.color);
          colorMap.set(`color-${Math.random().toString(36).substr(2, 9)}`, hsl);
        }
      });
    }
    
    if (node.children) {
      node.children.forEach(traverse);
    }
  }
  
  traverse(node);
  
  // Convert colors to tokens
  colorMap.forEach((value, key) => {
    tokens.push({
      name: key,
      value,
      type: 'color'
    });
  });
  
  return tokens;
}

function analyzeLayout(node: FigmaNode): LayoutInfo {
  return {
    direction: 'column', // Simplified - would analyze actual layout
    gap: 16,
    padding: { top: 16, right: 16, bottom: 16, left: 16 },
    alignment: 'start'
  };
}

function rgbaToHsl(color: FigmaColor): string {
  const { r, g, b, a = 1 } = color;
  
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const diff = max - min;
  const sum = max + min;
  const l = sum / 2;
  
  let h = 0;
  let s = 0;
  
  if (diff !== 0) {
    s = l < 0.5 ? diff / sum : diff / (2 - sum);
    
    switch (max) {
      case r:
        h = ((g - b) / diff) + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / diff + 2;
        break;
      case b:
        h = (r - g) / diff + 4;
        break;
    }
    h /= 6;
  }
  
  const hDeg = Math.round(h * 360);
  const sPercent = Math.round(s * 100);
  const lPercent = Math.round(l * 100);
  
  return a < 1 
    ? `hsla(${hDeg}, ${sPercent}%, ${lPercent}%, ${a})`
    : `hsl(${hDeg}, ${sPercent}%, ${lPercent}%)`;
}