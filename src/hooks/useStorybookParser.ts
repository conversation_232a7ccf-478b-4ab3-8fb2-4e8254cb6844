import { useState, useCallback } from 'react';

export interface StorybookStory {
  id: string;
  name: string;
  component: string;
  args: Record<string, any>;
  argTypes: Record<string, any>;
}

export interface StorybookData {
  stories: StorybookStory[];
  components: string[];
}

export const useStorybookParser = () => {
  const [storybookData, setStorybookData] = useState<StorybookData | null>(null);
  const [isParsing, setIsParsing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const parseStorybook = useCallback(async (storybookUrl: string) => {
    if (!storybookUrl) {
      setError('Storybook URL is required');
      return;
    }

    setIsParsing(true);
    setError(null);
    
    try {
      // Simulate Storybook API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const mockStorybookData: StorybookData = {
        stories: [
          {
            id: 'button--primary',
            name: 'Primary',
            component: 'Button',
            args: { variant: 'default', children: 'Button' },
            argTypes: {
              variant: { control: 'select', options: ['default', 'destructive', 'outline'] },
              size: { control: 'select', options: ['default', 'sm', 'lg'] }
            }
          },
          {
            id: 'card--default',
            name: 'Default',
            component: 'Card',
            args: { className: 'p-6' },
            argTypes: {
              className: { control: 'text' }
            }
          }
        ],
        components: ['Button', 'Card', 'Input', 'Badge', 'Alert']
      };
      
      setStorybookData(mockStorybookData);
    } catch (error) {
      setError('Failed to parse Storybook data');
      console.error('Storybook parsing error:', error);
    } finally {
      setIsParsing(false);
    }
  }, []);

  return {
    storybookData,
    isParsing,
    error,
    parseStorybook
  };
};