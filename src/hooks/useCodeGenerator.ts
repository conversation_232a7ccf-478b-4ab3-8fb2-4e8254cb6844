import { useState, useCallback } from 'react';
import { ComponentInfo, DesignToken, CodebaseAnalysis } from './useCodebaseAnalyzer';
import { StorybookData } from './useStorybookParser';
import { FigmaAnalysis } from './useFigmaAnalyzer';

export interface GenerationConfig {
  figmaData?: any;
  targetFramework: 'react' | 'vue' | 'angular';
  atomicLevel: 'atom' | 'molecule' | 'organism';
}

export interface GeneratedCode {
  componentName: string;
  code: string;
  imports: string[];
  storyCode?: string;
  dependencies: string[];
}

export const useCodeGenerator = () => {
  const [generatedCode, setGeneratedCode] = useState<GeneratedCode | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  const generateCode = useCallback(async (
    codebaseAnalysis: CodebaseAnalysis,
    storybookData: StorybookData,
    figmaAnalysis: FigmaAnalysis,
    config: GenerationConfig
  ) => {
    setIsGenerating(true);
    
    try {
      // Simulate AI code generation
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Generate code based on Figma analysis and learned patterns
      const componentName = figmaAnalysis.frame.name || 'GeneratedComponent';
      const reusableComponents = findMatchingComponents(codebaseAnalysis.components, figmaAnalysis);
      
      const code = generateReactComponent(
        componentName,
        reusableComponents,
        codebaseAnalysis.conventions,
        figmaAnalysis
      );
      
      const imports = generateImports(reusableComponents, codebaseAnalysis.conventions.importStyle);
      
      const storyCode = generateStorybookStory(componentName, reusableComponents);
      
      const result: GeneratedCode = {
        componentName,
        code,
        imports,
        storyCode,
        dependencies: reusableComponents.map(c => c.path)
      };
      
      setGeneratedCode(result);
    } catch (error) {
      console.error('Code generation failed:', error);
    } finally {
      setIsGenerating(false);
    }
  }, []);

  return {
    generatedCode,
    isGenerating,
    generateCode
  };
};

function findMatchingComponents(components: ComponentInfo[], figmaAnalysis: FigmaAnalysis): ComponentInfo[] {
  const matchedComponents: ComponentInfo[] = [];
  
  figmaAnalysis.extractedComponents.forEach(figmaComp => {
    const match = components.find(comp => {
      const compNameLower = comp.name.toLowerCase();
      const figmaTypeLower = figmaComp.type.toLowerCase();
      
      // Direct type matching
      if (compNameLower.includes(figmaTypeLower)) return true;
      
      // Component category matching
      if (figmaComp.type === 'button' && compNameLower.includes('button')) return true;
      if (figmaComp.type === 'input' && compNameLower.includes('input')) return true;
      if (figmaComp.type === 'card' && compNameLower.includes('card')) return true;
      
      return false;
    });
    
    if (match && !matchedComponents.find(c => c.name === match.name)) {
      matchedComponents.push(match);
    }
  });
  
  return matchedComponents;
}

function generateReactComponent(
  name: string,
  components: ComponentInfo[],
  conventions: any,
  figmaAnalysis: FigmaAnalysis
): string {
  const hasButton = components.some(c => c.name === 'Button');
  const hasCard = components.some(c => c.name === 'Card');
  
  return `export const ${name} = () => {
  return (
    <div className="space-y-4">
      ${hasCard ? `<Card className="p-6">
        <h2 className="text-lg font-semibold mb-4">${name} Component</h2>
        <p className="text-muted-foreground mb-4">
          Generated based on your codebase patterns and Storybook documentation.
        </p>
        ${hasButton ? `<Button variant="default">
          Primary Action
        </Button>` : '<div>Action Button</div>'}
      </Card>` : `<div className="p-6 border rounded-lg">
        <h2 className="text-lg font-semibold">${name}</h2>
        ${hasButton ? '<Button>Click me</Button>' : '<button>Click me</button>'}
      </div>`}
    </div>
  );
};`;
}

function generateImports(components: ComponentInfo[], importStyle: string): string[] {
  return components.map(comp => 
    importStyle.replace('ComponentName', comp.name).replace('component-name', comp.name.toLowerCase())
  );
}

function generateComponentJSX(figmaComp: any, availableComponents: ComponentInfo[]): string {
  const matchedComponent = availableComponents.find(comp => 
    comp.name.toLowerCase().includes(figmaComp.type.toLowerCase())
  );

  if (matchedComponent) {
    const props = generatePropsFromFigma(figmaComp, matchedComponent);
    return `<${matchedComponent.name}${props}>${figmaComp.properties.text || ''}</${matchedComponent.name}>`;
  }

  // Fallback to basic HTML elements
  switch (figmaComp.type) {
    case 'button':
      return `<button className="px-4 py-2 bg-primary text-primary-foreground rounded">${figmaComp.properties.text || 'Button'}</button>`;
    case 'text':
      return `<p className="text-foreground">${figmaComp.properties.text || 'Text'}</p>`;
    case 'input':
      return `<input className="px-3 py-2 border border-border rounded" placeholder="Input" />`;
    default:
      return `<div className="p-4 border border-border rounded">${figmaComp.name}</div>`;
  }
}

function generatePropsFromFigma(figmaComp: any, component: ComponentInfo): string {
  const props: string[] = [];
  
  // Match Figma properties to component variants
  if (component.variants && figmaComp.type === 'button') {
    props.push('variant="default"');
  }
  
  if (figmaComp.properties.className) {
    props.push(`className="${figmaComp.properties.className}"`);
  }

  return props.length > 0 ? ` ${props.join(' ')}` : '';
}

function generateStorybookStory(componentName: string, components: ComponentInfo[]): string {
  return `import type { Meta, StoryObj } from '@storybook/react';
import { ${componentName} } from './${componentName}';

const meta: Meta<typeof ${componentName}> = {
  title: 'Generated/${componentName}',
  component: ${componentName},
  parameters: {
    layout: 'centered',
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};`;
}