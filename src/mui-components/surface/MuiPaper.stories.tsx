import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { MuiPaper } from './MuiPaper';
import { 
  Typography, 
  Box, 
  Grid, 
  Button,
  Avatar,
  Stack,
  Divider,
} from '@mui/material';

const meta: Meta<typeof MuiPaper> = {
  title: 'MUI Components/Surface/Paper',
  component: MuiPaper,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Paper component for creating elevated surfaces with shadows and borders.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['elevation', 'outlined'],
    },
    elevation: {
      control: { type: 'range', min: 0, max: 24, step: 1 },
    },
  },
};

export default meta;
type Story = StoryObj<typeof MuiPaper>;

export const Default: Story = {
  args: {
    children: (
      <Typography variant="body1" sx={{ p: 2 }}>
        This is a basic paper component with default elevation.
      </Typography>
    ),
  },
};

export const WithPadding: Story = {
  args: {
    padding: 3,
    children: (
      <Typography variant="body1">
        This paper has custom padding applied.
      </Typography>
    ),
  },
};

export const Elevations: Story = {
  render: () => (
    <Grid container spacing={2} sx={{ width: 600 }}>
      {[0, 1, 2, 4, 6, 8, 12, 16, 24].map((elevation) => (
        <Grid item xs={4} key={elevation}>
          <MuiPaper elevation={elevation} padding={2} centered>
            <Typography variant="body2">
              Elevation {elevation}
            </Typography>
          </MuiPaper>
        </Grid>
      ))}
    </Grid>
  ),
};

export const Variants: Story = {
  render: () => (
    <Stack spacing={3} sx={{ width: 400 }}>
      <MuiPaper variant="elevation" elevation={3} padding={2}>
        <Typography variant="h6" gutterBottom>
          Elevation Variant
        </Typography>
        <Typography variant="body2">
          This paper uses the elevation variant with shadow.
        </Typography>
      </MuiPaper>
      
      <MuiPaper variant="outlined" padding={2}>
        <Typography variant="h6" gutterBottom>
          Outlined Variant
        </Typography>
        <Typography variant="body2">
          This paper uses the outlined variant with border.
        </Typography>
      </MuiPaper>
    </Stack>
  ),
};

export const Square: Story = {
  render: () => (
    <Stack direction="row" spacing={2}>
      <MuiPaper elevation={2} padding={2}>
        <Typography variant="body2">
          Rounded corners (default)
        </Typography>
      </MuiPaper>
      
      <MuiPaper elevation={2} padding={2} square>
        <Typography variant="body2">
          Square corners
        </Typography>
      </MuiPaper>
    </Stack>
  ),
};

export const CardExample: Story = {
  render: () => (
    <MuiPaper elevation={3} sx={{ maxWidth: 300 }}>
      <Box sx={{ p: 2 }}>
        <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 2 }}>
          <Avatar>JD</Avatar>
          <Box>
            <Typography variant="h6">John Doe</Typography>
            <Typography variant="body2" color="text.secondary">
              Software Engineer
            </Typography>
          </Box>
        </Stack>
        
        <Divider sx={{ my: 2 }} />
        
        <Typography variant="body2" paragraph>
          Passionate about creating beautiful and functional user interfaces. 
          Experienced in React, TypeScript, and Material-UI.
        </Typography>
        
        <Stack direction="row" spacing={1} justifyContent="flex-end">
          <Button size="small">View Profile</Button>
          <Button size="small" variant="contained">
            Connect
          </Button>
        </Stack>
      </Box>
    </MuiPaper>
  ),
};

export const DashboardWidget: Story = {
  render: () => (
    <MuiPaper elevation={2} padding={3} sx={{ width: 280 }}>
      <Typography variant="h6" gutterBottom>
        Monthly Sales
      </Typography>
      <Typography variant="h3" color="primary" gutterBottom>
        $24,500
      </Typography>
      <Typography variant="body2" color="success.main">
        +12% from last month
      </Typography>
      <Box sx={{ mt: 2 }}>
        <Button variant="outlined" size="small" fullWidth>
          View Details
        </Button>
      </Box>
    </MuiPaper>
  ),
};

export const FullWidth: Story = {
  render: () => (
    <Box sx={{ width: 600 }}>
      <MuiPaper elevation={1} padding={2} fullWidth>
        <Typography variant="h6" gutterBottom>
          Full Width Paper
        </Typography>
        <Typography variant="body1">
          This paper takes the full width of its container.
        </Typography>
      </MuiPaper>
    </Box>
  ),
};

export const Centered: Story = {
  render: () => (
    <MuiPaper 
      elevation={4} 
      centered 
      sx={{ width: 200, height: 150 }}
    >
      <Typography variant="h6" textAlign="center">
        Centered Content
      </Typography>
    </MuiPaper>
  ),
};

export const NestedPapers: Story = {
  render: () => (
    <MuiPaper elevation={6} padding={3} sx={{ width: 400 }}>
      <Typography variant="h5" gutterBottom>
        Outer Paper
      </Typography>
      
      <Stack spacing={2}>
        <MuiPaper elevation={2} padding={2} variant="outlined">
          <Typography variant="h6" gutterBottom>
            Nested Paper 1
          </Typography>
          <Typography variant="body2">
            This is a nested paper with outlined variant.
          </Typography>
        </MuiPaper>
        
        <MuiPaper elevation={3} padding={2}>
          <Typography variant="h6" gutterBottom>
            Nested Paper 2
          </Typography>
          <Typography variant="body2">
            This is another nested paper with elevation.
          </Typography>
        </MuiPaper>
      </Stack>
    </MuiPaper>
  ),
};

export const ColoredPaper: Story = {
  render: () => (
    <Stack spacing={2} sx={{ width: 300 }}>
      <MuiPaper 
        elevation={2} 
        padding={2}
        sx={{ backgroundColor: 'primary.light', color: 'primary.contrastText' }}
      >
        <Typography variant="body1">
          Primary colored paper
        </Typography>
      </MuiPaper>
      
      <MuiPaper 
        elevation={2} 
        padding={2}
        sx={{ backgroundColor: 'secondary.light', color: 'secondary.contrastText' }}
      >
        <Typography variant="body1">
          Secondary colored paper
        </Typography>
      </MuiPaper>
      
      <MuiPaper 
        elevation={2} 
        padding={2}
        sx={{ backgroundColor: 'error.light', color: 'error.contrastText' }}
      >
        <Typography variant="body1">
          Error colored paper
        </Typography>
      </MuiPaper>
    </Stack>
  ),
};
