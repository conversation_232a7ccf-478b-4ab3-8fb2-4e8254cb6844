import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { MuiAppBar } from './MuiAppBar';
import { 
  IconButton, 
  Button, 
  Avatar, 
  Badge,
  Box,
  Typography,
} from '@mui/material';
import {
  Search,
  Notifications,
  AccountCircle,
  MoreVert,
  Home,
} from '@mui/icons-material';

const meta: Meta<typeof MuiAppBar> = {
  title: 'MUI Components/Surface/AppBar',
  component: MuiAppBar,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'A Material-UI AppBar component for application headers and navigation.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    position: {
      control: { type: 'select' },
      options: ['fixed', 'absolute', 'sticky', 'static', 'relative'],
    },
    color: {
      control: { type: 'select' },
      options: ['default', 'inherit', 'primary', 'secondary', 'transparent'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof MuiAppBar>;

export const Default: Story = {
  args: {
    title: 'My Application',
  },
};

export const WithMenuIcon: Story = {
  args: {
    title: 'My Application',
    showMenuIcon: true,
    onMenuClick: () => console.log('Menu clicked'),
  },
};

export const WithActions: Story = {
  args: {
    title: 'My Application',
    actions: [
      <IconButton color="inherit">
        <Search />
      </IconButton>,
      <IconButton color="inherit">
        <Badge badgeContent={4} color="error">
          <Notifications />
        </Badge>
      </IconButton>,
      <IconButton color="inherit">
        <AccountCircle />
      </IconButton>,
    ],
  },
};

export const WithLogo: Story = {
  args: {
    title: 'My Application',
    logo: (
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <Home sx={{ mr: 1 }} />
      </Box>
    ),
    actions: [
      <Button color="inherit">Login</Button>,
    ],
  },
};

export const Dense: Story = {
  args: {
    title: 'Dense App Bar',
    dense: true,
    showMenuIcon: true,
    actions: [
      <IconButton color="inherit" size="small">
        <Search />
      </IconButton>,
      <IconButton color="inherit" size="small">
        <MoreVert />
      </IconButton>,
    ],
  },
};

export const CustomContent: Story = {
  args: {
    showMenuIcon: true,
    onMenuClick: () => console.log('Menu clicked'),
    children: (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <Typography variant="h6" component="div">
          Dashboard
        </Typography>
        <Button color="inherit">Projects</Button>
        <Button color="inherit">Team</Button>
        <Button color="inherit">Reports</Button>
      </Box>
    ),
    actions: [
      <Avatar sx={{ width: 32, height: 32 }}>U</Avatar>,
    ],
  },
};

export const Colors: Story = {
  render: () => (
    <Box sx={{ flexGrow: 1 }}>
      <MuiAppBar title="Primary (default)" color="primary" sx={{ mb: 1 }} />
      <MuiAppBar title="Secondary" color="secondary" sx={{ mb: 1 }} />
      <MuiAppBar title="Default" color="default" sx={{ mb: 1 }} />
      <MuiAppBar title="Inherit" color="inherit" sx={{ mb: 1 }} />
      <MuiAppBar title="Transparent" color="transparent" />
    </Box>
  ),
};

export const Positions: Story = {
  render: () => (
    <Box sx={{ height: '100vh', position: 'relative' }}>
      <MuiAppBar 
        title="Static Position" 
        position="static"
        actions={[<Button color="inherit">Action</Button>]}
      />
      <Box sx={{ p: 2 }}>
        <Typography variant="body1" gutterBottom>
          This is content below the static app bar. The static position is the default and doesn't affect the document flow.
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Other positions like 'fixed', 'absolute', and 'sticky' would behave differently in a real application.
        </Typography>
      </Box>
    </Box>
  ),
};

export const SearchAppBar: Story = {
  args: {
    showMenuIcon: true,
    children: (
      <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
        <Typography variant="h6" component="div" sx={{ mr: 2 }}>
          Search App
        </Typography>
        <Box
          sx={{
            position: 'relative',
            borderRadius: 1,
            backgroundColor: 'rgba(255, 255, 255, 0.15)',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.25)',
            },
            marginLeft: 0,
            width: '100%',
            maxWidth: 400,
          }}
        >
          <Box
            sx={{
              padding: '0 16px',
              height: '100%',
              position: 'absolute',
              pointerEvents: 'none',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Search />
          </Box>
          <input
            style={{
              padding: '8px 8px 8px 48px',
              border: 'none',
              background: 'transparent',
              color: 'inherit',
              width: '100%',
              outline: 'none',
            }}
            placeholder="Search…"
          />
        </Box>
      </Box>
    ),
    actions: [
      <IconButton color="inherit">
        <AccountCircle />
      </IconButton>,
    ],
  },
};

export const ProfileAppBar: Story = {
  args: {
    title: 'Profile Dashboard',
    showMenuIcon: true,
    actions: [
      <IconButton color="inherit">
        <Badge badgeContent={17} color="error">
          <Notifications />
        </Badge>
      </IconButton>,
      <Button color="inherit" startIcon={<AccountCircle />}>
        John Doe
      </Button>,
    ],
  },
};
