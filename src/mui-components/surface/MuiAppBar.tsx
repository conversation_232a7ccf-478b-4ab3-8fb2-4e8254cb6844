import React from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Button,
  AppBarProps,
  Box,
} from '@mui/material';
import { Menu as MenuIcon } from '@mui/icons-material';

export interface MuiAppBarProps extends AppBarProps {
  /**
   * The title of the app bar
   */
  title?: string;
  /**
   * Whether to show the menu icon
   */
  showMenuIcon?: boolean;
  /**
   * Callback when menu icon is clicked
   */
  onMenuClick?: () => void;
  /**
   * Actions to display on the right side
   */
  actions?: React.ReactNode[];
  /**
   * Custom content for the toolbar
   */
  children?: React.ReactNode;
  /**
   * Whether the app bar should be dense
   */
  dense?: boolean;
  /**
   * Logo element
   */
  logo?: React.ReactNode;
  /**
   * Whether to disable gutters
   */
  disableGutters?: boolean;
}

/**
 * A Material-UI AppBar component for application headers and navigation
 */
export const MuiAppBar: React.FC<MuiAppBarProps> = ({
  title,
  showMenuIcon = false,
  onMenuClick,
  actions = [],
  children,
  dense = false,
  logo,
  disableGutters = false,
  position = 'static',
  color = 'primary',
  ...props
}) => {
  return (
    <AppBar position={position} color={color} {...props}>
      <Toolbar variant={dense ? 'dense' : 'regular'} disableGutters={disableGutters}>
        {showMenuIcon && (
          <IconButton
            size="large"
            edge="start"
            color="inherit"
            aria-label="menu"
            onClick={onMenuClick}
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>
        )}
        
        {logo && (
          <Box sx={{ mr: 2, display: 'flex', alignItems: 'center' }}>
            {logo}
          </Box>
        )}
        
        {title && (
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            {title}
          </Typography>
        )}
        
        {children && (
          <Box sx={{ flexGrow: 1 }}>
            {children}
          </Box>
        )}
        
        {actions.length > 0 && (
          <Box sx={{ display: 'flex', gap: 1 }}>
            {actions.map((action, index) => (
              <Box key={index}>{action}</Box>
            ))}
          </Box>
        )}
      </Toolbar>
    </AppBar>
  );
};
