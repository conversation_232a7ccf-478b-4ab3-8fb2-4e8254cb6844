import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MuiAccordion } from './MuiAccordion';
import { Typography, List, ListItem, ListItemText } from '@mui/material';

const meta: Meta<typeof MuiAccordion> = {
  title: 'MUI Components/Surface/Accordion',
  component: MuiAccordion,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Accordion component for organizing content in collapsible sections.',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

const basicItems = [
  {
    id: 'panel1',
    title: 'General Settings',
    content: 'This is where you can configure general application settings.',
  },
  {
    id: 'panel2',
    title: 'User Preferences',
    content: 'Customize your user experience and preferences here.',
  },
  {
    id: 'panel3',
    title: 'Advanced Options',
    content: 'Advanced configuration options for power users.',
  },
];

const faqItems = [
  {
    id: 'faq1',
    title: 'What is Material-UI?',
    content: 'Material-UI is a popular React UI framework that implements Google\'s Material Design.',
  },
  {
    id: 'faq2',
    title: 'How do I install Material-UI?',
    content: 'You can install Material-UI using npm: npm install @mui/material @emotion/react @emotion/styled',
  },
  {
    id: 'faq3',
    title: 'Is Material-UI free to use?',
    content: 'Yes, Material-UI is open source and free to use under the MIT license.',
  },
  {
    id: 'faq4',
    title: 'Can I customize the theme?',
    content: 'Absolutely! Material-UI provides a comprehensive theming system that allows you to customize colors, typography, spacing, and more.',
    disabled: true,
  },
];

export const Default: Story = {
  args: {
    items: basicItems,
  },
};

export const MultipleExpanded: Story = {
  args: {
    items: basicItems,
    multiple: true,
    defaultExpanded: ['panel1', 'panel2'],
  },
};

export const FAQ: Story = {
  args: {
    items: faqItems,
    defaultExpanded: 'faq1',
  },
};

export const WithComplexContent: Story = {
  args: {
    items: [
      {
        id: 'features',
        title: 'Product Features',
        content: (
          <List>
            <ListItem>
              <ListItemText primary="Feature 1" secondary="Description of feature 1" />
            </ListItem>
            <ListItem>
              <ListItemText primary="Feature 2" secondary="Description of feature 2" />
            </ListItem>
            <ListItem>
              <ListItemText primary="Feature 3" secondary="Description of feature 3" />
            </ListItem>
          </List>
        ),
      },
      {
        id: 'pricing',
        title: 'Pricing Information',
        content: (
          <div>
            <Typography variant="h6" gutterBottom>
              Subscription Plans
            </Typography>
            <Typography variant="body2" paragraph>
              Basic Plan: $9.99/month
            </Typography>
            <Typography variant="body2" paragraph>
              Pro Plan: $19.99/month
            </Typography>
            <Typography variant="body2">
              Enterprise Plan: Contact us for pricing
            </Typography>
          </div>
        ),
      },
      {
        id: 'support',
        title: 'Support & Contact',
        content: (
          <div>
            <Typography variant="body1" paragraph>
              Need help? We're here to assist you!
            </Typography>
            <Typography variant="body2">
              Email: <EMAIL><br />
              Phone: +****************<br />
              Hours: Monday-Friday, 9 AM - 5 PM EST
            </Typography>
          </div>
        ),
      },
    ],
  },
};
