import React from 'react';
import { Paper, PaperProps } from '@mui/material';

export interface MuiPaperProps extends PaperProps {
  /**
   * The content of the paper
   */
  children: React.ReactNode;
  /**
   * The elevation of the paper
   */
  elevation?: number;
  /**
   * The variant of the paper
   */
  variant?: 'elevation' | 'outlined';
  /**
   * Whether the paper should be square (no border radius)
   */
  square?: boolean;
  /**
   * Custom padding
   */
  padding?: number | string;
  /**
   * Custom margin
   */
  margin?: number | string;
  /**
   * Whether the paper should take full width
   */
  fullWidth?: boolean;
  /**
   * Whether the paper should take full height
   */
  fullHeight?: boolean;
  /**
   * Whether the paper should be centered
   */
  centered?: boolean;
}

/**
 * A Material-UI Paper component for creating elevated surfaces
 */
export const MuiPaper: React.FC<MuiPaperProps> = ({
  children,
  elevation = 1,
  variant = 'elevation',
  square = false,
  padding,
  margin,
  fullWidth = false,
  fullHeight = false,
  centered = false,
  sx,
  ...props
}) => {
  const customSx = {
    ...(padding && { p: padding }),
    ...(margin && { m: margin }),
    ...(fullWidth && { width: '100%' }),
    ...(fullHeight && { height: '100%' }),
    ...(centered && {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }),
    ...sx,
  };

  return (
    <Paper
      elevation={elevation}
      variant={variant}
      square={square}
      sx={customSx}
      {...props}
    >
      {children}
    </Paper>
  );
};
