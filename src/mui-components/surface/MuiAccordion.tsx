import React from 'react';
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  AccordionProps,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

export interface AccordionItem {
  id: string;
  title: string;
  content: React.ReactNode;
  disabled?: boolean;
}

export interface MuiAccordionProps extends Omit<AccordionProps, 'children'> {
  /**
   * Array of accordion items
   */
  items: AccordionItem[];
  /**
   * Whether multiple panels can be expanded at once
   */
  multiple?: boolean;
  /**
   * Default expanded panel(s)
   */
  defaultExpanded?: string | string[];
  /**
   * Controlled expanded panel(s)
   */
  expanded?: string | string[];
  /**
   * Callback when expansion changes
   */
  onChange?: (panelId: string, isExpanded: boolean) => void;
}

/**
 * A Material-UI Accordion component for collapsible content sections
 */
export const MuiAccordion: React.FC<MuiAccordionProps> = ({
  items,
  multiple = false,
  defaultExpanded,
  expanded,
  onChange,
  ...props
}) => {
  const [internalExpanded, setInternalExpanded] = React.useState<string[]>(() => {
    if (defaultExpanded) {
      return Array.isArray(defaultExpanded) ? defaultExpanded : [defaultExpanded];
    }
    return [];
  });

  const expandedPanels = expanded 
    ? (Array.isArray(expanded) ? expanded : [expanded])
    : internalExpanded;

  const handleChange = (panelId: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    if (onChange) {
      onChange(panelId, isExpanded);
    }

    if (expanded === undefined) {
      setInternalExpanded(prev => {
        if (multiple) {
          return isExpanded 
            ? [...prev, panelId]
            : prev.filter(id => id !== panelId);
        } else {
          return isExpanded ? [panelId] : [];
        }
      });
    }
  };

  return (
    <>
      {items.map((item) => (
        <Accordion
          key={item.id}
          expanded={expandedPanels.includes(item.id)}
          onChange={handleChange(item.id)}
          disabled={item.disabled}
          {...props}
        >
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            aria-controls={`${item.id}-content`}
            id={`${item.id}-header`}
          >
            <Typography>{item.title}</Typography>
          </AccordionSummary>
          <AccordionDetails>
            {typeof item.content === 'string' ? (
              <Typography>{item.content}</Typography>
            ) : (
              item.content
            )}
          </AccordionDetails>
        </Accordion>
      ))}
    </>
  );
};
