# 🎨 MUI Component Library (Reusable Components)

This directory contains a comprehensive collection of **reusable Material-UI (MUI) components** organized by category, each with complete Storybook stories and TypeScript support.

> ⚠️ **Important**: This is a **reusable component library**. These components should be framework-agnostic and must not import from application-specific code.

## 🏗️ **Architecture**

```
src/mui-components/          # REUSABLE MUI LIBRARY
├── inputs/                  # Input components
├── data-display/           # Data display components
├── feedback/               # Feedback components
├── surface/                # Surface components
├── navigation/             # Navigation components
├── layout/                 # Layout components
├── lab/                    # Experimental components
├── index.ts               # Library exports
└── README.md              # This file

src/components/             # APPLICATION-SPECIFIC COMPONENTS
├── AnalysisResults.tsx    # App-specific components
├── ConfigurationPanel.tsx # App-specific components
├── ui/                    # shadcn/ui components
└── index.ts              # App exports
```

## 📁 Component Organization

### 🔧 Inputs (13 components)
Components for user input and interaction:
- **MuiAutocomplete** - Searchable dropdown with autocomplete functionality
- **MuiButton** - Standard button component with variants and colors
- **MuiButtonGroup** - Grouped buttons for related actions
- **MuiCheckbox** - Checkbox input with optional labels
- **MuiFloatingActionButton** - Floating action button for primary actions
- **MuiRadioGroup** - Radio button group for single selection
- **MuiRating** - Star rating component for feedback
- **MuiSelect** - Dropdown selection with options
- **MuiSlider** - Range slider for numeric input
- **MuiSwitch** - Toggle switch for boolean input
- **MuiTextField** - Text input field with various types and validation
- **MuiToggleButton/Group** - Toggle button selections
- **MuiTransferList** - Move items between lists

### 📊 Data Display (10 components)
Components for displaying information:
- **MuiAvatar** - User profile pictures or initials
- **MuiBadge** - Notification badges and status indicators
- **MuiChip** - Compact information tags
- **MuiDivider** - Content separators
- **MuiIcon** - Material Design icons collection
- **MuiList** - Structured data lists with nesting
- **MuiTable** - Data tables with sorting and pagination
- **MuiTooltip** - Helpful hover information
- **MuiTypography** - Consistent text styling

### 💬 Feedback (6 components)
Components for user feedback and notifications:
- **MuiAlert** - Alert messages with different severity levels
- **MuiBackdrop** - Overlay blocking interactions
- **MuiDialog** - Modal dialogs for confirmations and forms
- **MuiProgress** - Circular & Linear progress indicators
- **MuiSkeleton** - Loading placeholders
- **MuiSnackbar** - Brief notification messages

### 🏗️ Surface (4 components)
Container components:
- **MuiAccordion** - Collapsible content sections
- **MuiAppBar** - Application headers and navigation
- **MuiCard** - Content cards with actions and media
- **MuiPaper** - Elevated surfaces with shadows

### 🧭 Navigation (4 components)
Components for navigation:
- **MuiBreadcrumbs** - Navigation hierarchy
- **MuiLink** - Navigation and external links
- **MuiPagination** - Page navigation controls
- **MuiTabs** - Tabbed content organization

### 📐 Layout (6 components)
Components for page layout:
- **MuiBox** - Layout and styling utility
- **MuiContainer** - Responsive content containers
- **MuiGrid** - Responsive grid system using Grid2
- **MuiGridLegacy** - Legacy Grid v1 system
- **MuiImageList** - Image gallery layouts
- **MuiStack** - One-dimensional layouts

### 🧪 Lab (4 components)
Experimental and advanced components:
- **MuiDatePicker** - Date selection with calendar
- **MuiMasonry** - Pinterest-like layouts
- **MuiTimeline** - Chronological information display
- **MuiTreeView** - Hierarchical data structures

## 📊 Component Statistics

- **Total Components**: 50+
- **Categories**: 7 (including Lab)
- **TypeScript Coverage**: 100%
- **Storybook Stories**: 100%
- **Accessibility Features**: ✅
- **Material-UI Coverage**: ~85% of core components

## 🚀 Usage

### **Import Guidelines**

✅ **DO**: Import reusable MUI components from `@/mui-components`
✅ **DO**: Import app-specific components from `@/components`
❌ **DON'T**: Import from `@/mui-components` in application-specific components
❌ **DON'T**: Import application code in MUI components

```typescript
// ✅ CORRECT: Import from MUI component library
import { MuiButton, MuiTextField, MuiCard } from '@/mui-components';

// ✅ CORRECT: Import app-specific components separately
import { AnalysisResults } from '@/components';
```

### **Basic Example**
```typescript
import { MuiButton, MuiCard, MuiAlert } from '@/mui-components';

function MyComponent() {
  return (
    <MuiCard title="Example">
      <MuiAlert severity="info">This is an info alert</MuiAlert>
      <MuiButton
        variant="contained"
        color="primary"
        onClick={() => console.log('Clicked!')}
      >
        Click Me
      </MuiButton>
    </MuiCard>
  );
}
```

### **Storybook Access**

All MUI components are documented in Storybook:
- **Local**: http://localhost:6006/
- **Categories**: Inputs, Data Display, Feedback, Surface, Navigation, Layout, Lab

## 🎨 Theming

All components are integrated with the MUI theme system. The theme can be customized in `src/theme/mui-theme.ts`:

```typescript
import { createTheme } from '@mui/material/styles';

export const muiTheme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});
```

## 📖 Storybook Integration

Each component includes comprehensive Storybook stories demonstrating:
- **Default usage** - Basic component with default props
- **Variants** - Different visual styles and configurations
- **Interactive examples** - Controlled components with state
- **Edge cases** - Error states, disabled states, etc.
- **Accessibility** - Proper ARIA labels and keyboard navigation

### Viewing Stories
1. Start Storybook: `npm run storybook`
2. Navigate to http://localhost:6006
3. Browse components by category in the sidebar
4. Use the Controls panel to modify props in real-time
5. Check the Docs tab for component documentation

## 🔧 Component Features

### TypeScript Support
- Full TypeScript definitions for all props
- Extends native MUI component props
- Comprehensive JSDoc documentation

### Accessibility
- ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility
- Focus management

### Responsive Design
- Mobile-first approach
- Breakpoint-aware components
- Flexible sizing options

### Customization
- Theme integration
- Custom styling via sx prop
- Extensible prop interfaces

## 🧪 Testing

Components are designed to be easily testable:
- Semantic HTML structure
- Predictable prop interfaces
- Accessible selectors
- Controlled state management

## 📝 Contributing

When adding new components:
1. Create component in appropriate category folder
2. Include comprehensive TypeScript types
3. Add Storybook stories with multiple examples
4. Update the index.ts export file
5. Add documentation to this README

## 🔗 Related Files

- `src/theme/mui-theme.ts` - Theme configuration
- `.storybook/preview.ts` - Storybook MUI integration
- `src/components/index.ts` - Component exports
