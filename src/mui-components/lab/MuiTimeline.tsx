import React from 'react';
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent,
  TimelineProps,
} from '@mui/lab';
import { Typography } from '@mui/material';

export interface TimelineItemData {
  id: string | number;
  title: string;
  description?: string;
  time?: string;
  icon?: React.ReactElement;
  color?: 'inherit' | 'grey' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  variant?: 'filled' | 'outlined';
}

export interface MuiTimelineProps extends TimelineProps {
  /**
   * Array of timeline items
   */
  items: TimelineItemData[];
  /**
   * Position of the timeline
   */
  position?: 'left' | 'right' | 'alternate';
  /**
   * Whether to show opposite content (time/date)
   */
  showOppositeContent?: boolean;
  /**
   * Whether to show connectors between items
   */
  showConnectors?: boolean;
}

/**
 * A Material-UI Timeline component for displaying chronological information
 */
export const MuiTimeline: React.FC<MuiTimelineProps> = ({
  items,
  position = 'right',
  showOppositeContent = true,
  showConnectors = true,
  ...props
}) => {
  return (
    <Timeline position={position} {...props}>
      {items.map((item, index) => (
        <TimelineItem key={item.id}>
          {showOppositeContent && item.time && (
            <TimelineOppositeContent
              sx={{ m: 'auto 0' }}
              align={position === 'left' ? 'left' : 'right'}
              variant="body2"
              color="text.secondary"
            >
              {item.time}
            </TimelineOppositeContent>
          )}
          
          <TimelineSeparator>
            <TimelineDot
              color={item.color || 'primary'}
              variant={item.variant || 'filled'}
            >
              {item.icon}
            </TimelineDot>
            {showConnectors && index < items.length - 1 && <TimelineConnector />}
          </TimelineSeparator>
          
          <TimelineContent sx={{ py: '12px', px: 2 }}>
            <Typography variant="h6" component="span">
              {item.title}
            </Typography>
            {item.description && (
              <Typography variant="body2" color="text.secondary">
                {item.description}
              </Typography>
            )}
          </TimelineContent>
        </TimelineItem>
      ))}
    </Timeline>
  );
};
