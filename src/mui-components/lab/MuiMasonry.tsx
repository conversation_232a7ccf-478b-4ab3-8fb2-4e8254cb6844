import React from 'react';
import { Masonry } from '@mui/lab';
import { Box, BoxProps } from '@mui/material';

export interface MasonryItem {
  id: string | number;
  content: React.ReactNode;
  height?: number;
}

export interface MuiMasonryProps extends Omit<BoxProps, 'children'> {
  /**
   * Array of masonry items
   */
  items: MasonryItem[];
  /**
   * Number of columns
   */
  columns?: number | { [key: string]: number };
  /**
   * Spacing between items
   */
  spacing?: number;
  /**
   * Default height for items (if not specified)
   */
  defaultHeight?: number;
  /**
   * Sequential or not
   */
  sequential?: boolean;
}

/**
 * A Material-UI Masonry component for creating Pinterest-like layouts
 */
export const MuiMasonry: React.FC<MuiMasonryProps> = ({
  items,
  columns = 4,
  spacing = 1,
  defaultHeight = 200,
  sequential = false,
  ...props
}) => {
  return (
    <Box {...props}>
      <Masonry columns={columns} spacing={spacing} sequential={sequential}>
        {items.map((item) => (
          <Box
            key={item.id}
            sx={{
              height: item.height || defaultHeight,
              backgroundColor: 'grey.100',
              borderRadius: 1,
              overflow: 'hidden',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {item.content}
          </Box>
        ))}
      </Masonry>
    </Box>
  );
};
