import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MuiTimeline } from './MuiTimeline';
import {
  Fastfood,
  LaptopMac,
  Hotel,
  Repeat,
  Work,
  School,
  Home,
} from '@mui/icons-material';

const meta: Meta<typeof MuiTimeline> = {
  title: 'MUI Components/Lab/Timeline',
  component: MuiTimeline,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Timeline component for displaying chronological information.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    position: {
      control: { type: 'select' },
      options: ['left', 'right', 'alternate'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof MuiTimeline>;

const basicItems = [
  {
    id: 1,
    title: 'Eat',
    description: 'Because you need strength',
    time: '9:30 am',
    icon: <Fastfood />,
    color: 'primary' as const,
  },
  {
    id: 2,
    title: 'Code',
    description: 'Because it\'s awesome!',
    time: '10:00 am',
    icon: <LaptopMac />,
    color: 'secondary' as const,
  },
  {
    id: 3,
    title: 'Sleep',
    description: 'Because you need rest',
    time: '12:00 am',
    icon: <Hotel />,
    color: 'success' as const,
  },
  {
    id: 4,
    title: 'Repeat',
    description: 'Because this is the life you love!',
    time: '9:00 am',
    icon: <Repeat />,
    color: 'warning' as const,
  },
];

const careerItems = [
  {
    id: 1,
    title: 'Education',
    description: 'Computer Science Degree',
    time: '2018-2022',
    icon: <School />,
    color: 'primary' as const,
  },
  {
    id: 2,
    title: 'First Job',
    description: 'Junior Developer at Tech Corp',
    time: '2022-2023',
    icon: <Work />,
    color: 'secondary' as const,
  },
  {
    id: 3,
    title: 'Promotion',
    description: 'Senior Developer',
    time: '2023-2024',
    icon: <Work />,
    color: 'success' as const,
  },
  {
    id: 4,
    title: 'Remote Work',
    description: 'Working from home',
    time: '2024-Present',
    icon: <Home />,
    color: 'info' as const,
  },
];

export const Default: Story = {
  args: {
    items: basicItems,
  },
};

export const LeftPosition: Story = {
  args: {
    items: basicItems,
    position: 'left',
  },
};

export const AlternatePosition: Story = {
  args: {
    items: careerItems,
    position: 'alternate',
  },
};

export const WithoutOppositeContent: Story = {
  args: {
    items: basicItems.map(item => ({ ...item, time: undefined })),
    showOppositeContent: false,
  },
};

export const WithoutConnectors: Story = {
  args: {
    items: basicItems,
    showConnectors: false,
  },
};

export const OutlinedDots: Story = {
  args: {
    items: basicItems.map(item => ({ ...item, variant: 'outlined' as const })),
  },
};

export const CareerTimeline: Story = {
  args: {
    items: careerItems,
    position: 'alternate',
  },
};
