import React from 'react';
import { DatePicker, DatePickerProps } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { TextField } from '@mui/material';

export interface MuiDatePickerProps extends Omit<DatePickerProps<Date>, 'renderInput'> {
  /**
   * The label for the date picker
   */
  label?: string;
  /**
   * Helper text
   */
  helperText?: string;
  /**
   * Error state
   */
  error?: boolean;
  /**
   * Whether the field is required
   */
  required?: boolean;
  /**
   * Whether the field is disabled
   */
  disabled?: boolean;
  /**
   * Input variant
   */
  variant?: 'outlined' | 'filled' | 'standard';
  /**
   * Size of the input
   */
  size?: 'small' | 'medium';
  /**
   * Whether to show the calendar icon
   */
  showCalendarIcon?: boolean;
}

/**
 * A Material-UI DatePicker component for selecting dates
 */
export const MuiDatePicker: React.FC<MuiDatePickerProps> = ({
  label = 'Select Date',
  helperText,
  error = false,
  required = false,
  disabled = false,
  variant = 'outlined',
  size = 'medium',
  showCalendarIcon = true,
  value,
  onChange,
  ...props
}) => {
  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <DatePicker
        label={label}
        value={value}
        onChange={onChange}
        disabled={disabled}
        slotProps={{
          textField: {
            variant,
            size,
            error,
            helperText,
            required,
            fullWidth: true,
          },
        }}
        {...props}
      />
    </LocalizationProvider>
  );
};
