import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MuiTreeView } from './MuiTreeView';
import { useState } from 'react';
import { Paper, Typography } from '@mui/material';
import {
  Folder,
  Description,
  Image,
  VideoFile,
  AudioFile,
  Code,
} from '@mui/icons-material';

const meta: Meta<typeof MuiTreeView> = {
  title: 'MUI Components/Lab/TreeView',
  component: MuiTreeView,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI TreeView component for displaying hierarchical data structures.',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof MuiTreeView>;

const fileSystemNodes = [
  {
    id: 'root',
    label: 'Project Root',
    children: [
      {
        id: 'src',
        label: 'src',
        icon: <Folder />,
        children: [
          {
            id: 'components',
            label: 'components',
            icon: <Folder />,
            children: [
              { id: 'button.tsx', label: 'Button.tsx', icon: <Code /> },
              { id: 'input.tsx', label: 'Input.tsx', icon: <Code /> },
            ],
          },
          {
            id: 'pages',
            label: 'pages',
            icon: <Folder />,
            children: [
              { id: 'home.tsx', label: 'Home.tsx', icon: <Code /> },
              { id: 'about.tsx', label: 'About.tsx', icon: <Code /> },
            ],
          },
        ],
      },
      {
        id: 'public',
        label: 'public',
        icon: <Folder />,
        children: [
          { id: 'index.html', label: 'index.html', icon: <Description /> },
          { id: 'favicon.ico', label: 'favicon.ico', icon: <Image /> },
        ],
      },
      { id: 'package.json', label: 'package.json', icon: <Description /> },
      { id: 'readme.md', label: 'README.md', icon: <Description /> },
    ],
  },
];

const mediaNodes = [
  {
    id: 'media',
    label: 'Media Library',
    children: [
      {
        id: 'images',
        label: 'Images',
        icon: <Folder />,
        children: [
          { id: 'photo1.jpg', label: 'photo1.jpg', icon: <Image /> },
          { id: 'photo2.png', label: 'photo2.png', icon: <Image /> },
          { id: 'logo.svg', label: 'logo.svg', icon: <Image /> },
        ],
      },
      {
        id: 'videos',
        label: 'Videos',
        icon: <Folder />,
        children: [
          { id: 'intro.mp4', label: 'intro.mp4', icon: <VideoFile /> },
          { id: 'demo.avi', label: 'demo.avi', icon: <VideoFile /> },
        ],
      },
      {
        id: 'audio',
        label: 'Audio',
        icon: <Folder />,
        children: [
          { id: 'music.mp3', label: 'music.mp3', icon: <AudioFile /> },
          { id: 'sound.wav', label: 'sound.wav', icon: <AudioFile /> },
        ],
      },
    ],
  },
];

export const Default: Story = {
  args: {
    nodes: fileSystemNodes,
  },
};

export const Controlled: Story = {
  render: (args) => {
    const [selectedItems, setSelectedItems] = useState<string[]>([]);
    const [expandedItems, setExpandedItems] = useState<string[]>(['root', 'src']);
    
    return (
      <Paper sx={{ p: 2, width: 300 }}>
        <Typography variant="h6" gutterBottom>
          File Explorer
        </Typography>
        <Typography variant="body2" gutterBottom>
          Selected: {selectedItems.join(', ') || 'None'}
        </Typography>
        <MuiTreeView
          {...args}
          selectedItems={selectedItems}
          expandedItems={expandedItems}
          onSelectedItemsChange={(event, itemIds) => {
            setSelectedItems(Array.isArray(itemIds) ? itemIds : itemIds ? [itemIds] : []);
          }}
          onExpandedItemsChange={(event, itemIds) => setExpandedItems(itemIds)}
        />
      </Paper>
    );
  },
  args: {
    nodes: fileSystemNodes,
  },
};

export const MultiSelect: Story = {
  render: (args) => {
    const [selectedItems, setSelectedItems] = useState<string[]>([]);
    
    return (
      <Paper sx={{ p: 2, width: 300 }}>
        <Typography variant="h6" gutterBottom>
          Multi-Select Tree
        </Typography>
        <Typography variant="body2" gutterBottom>
          Selected: {selectedItems.length} items
        </Typography>
        <MuiTreeView
          {...args}
          selectedItems={selectedItems}
          onSelectedItemsChange={(event, itemIds) => {
            setSelectedItems(Array.isArray(itemIds) ? itemIds : itemIds ? [itemIds] : []);
          }}
        />
      </Paper>
    );
  },
  args: {
    nodes: mediaNodes,
    multiSelect: true,
  },
};

export const WithoutIcons: Story = {
  args: {
    nodes: [
      {
        id: 'categories',
        label: 'Categories',
        children: [
          {
            id: 'electronics',
            label: 'Electronics',
            children: [
              { id: 'phones', label: 'Phones' },
              { id: 'laptops', label: 'Laptops' },
            ],
          },
          {
            id: 'clothing',
            label: 'Clothing',
            children: [
              { id: 'shirts', label: 'Shirts' },
              { id: 'pants', label: 'Pants' },
            ],
          },
        ],
      },
    ],
    showDefaultIcons: false,
  },
};

export const WithDisabledItems: Story = {
  args: {
    nodes: [
      {
        id: 'root',
        label: 'Root Folder',
        children: [
          { id: 'file1', label: 'Available File' },
          { id: 'file2', label: 'Disabled File', disabled: true },
          {
            id: 'folder1',
            label: 'Available Folder',
            children: [
              { id: 'nested1', label: 'Nested File' },
            ],
          },
          {
            id: 'folder2',
            label: 'Disabled Folder',
            disabled: true,
            children: [
              { id: 'nested2', label: 'Nested File' },
            ],
          },
        ],
      },
    ],
  },
};
