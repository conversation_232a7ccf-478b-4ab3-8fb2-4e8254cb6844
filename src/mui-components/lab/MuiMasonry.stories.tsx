import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { MuiMasonry } from './MuiMasonry';
import { Typography, Card, CardContent, CardMedia } from '@mui/material';

const meta: Meta<typeof MuiMasonry> = {
  title: 'MUI Components/Lab/Masonry',
  component: MuiMasonry,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'A Material-UI Masonry component for creating Pinterest-like layouts with varying item heights.',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof MuiMasonry>;

const basicItems = Array.from({ length: 12 }, (_, index) => ({
  id: index + 1,
  content: (
    <Typography variant="h6" textAlign="center">
      Item {index + 1}
    </Typography>
  ),
  height: Math.floor(Math.random() * 200) + 100,
}));

const cardItems = [
  {
    id: 1,
    content: (
      <Card>
        <CardMedia
          component="img"
          height="140"
          image="https://images.unsplash.com/photo-1551963831-b3b1ca40c98e?w=300"
          alt="Food"
        />
        <CardContent>
          <Typography variant="h6">Delicious Food</Typography>
          <Typography variant="body2" color="text.secondary">
            Amazing breakfast spread
          </Typography>
        </CardContent>
      </Card>
    ),
    height: 250,
  },
  {
    id: 2,
    content: (
      <Card>
        <CardContent>
          <Typography variant="h6">Quote of the Day</Typography>
          <Typography variant="body1" sx={{ fontStyle: 'italic', mt: 1 }}>
            "The only way to do great work is to love what you do."
          </Typography>
          <Typography variant="caption" sx={{ mt: 1, display: 'block' }}>
            - Steve Jobs
          </Typography>
        </CardContent>
      </Card>
    ),
    height: 180,
  },
  {
    id: 3,
    content: (
      <Card>
        <CardMedia
          component="img"
          height="200"
          image="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300"
          alt="Mountain"
        />
        <CardContent>
          <Typography variant="h6">Mountain View</Typography>
          <Typography variant="body2" color="text.secondary">
            Beautiful landscape photography from the mountains
          </Typography>
        </CardContent>
      </Card>
    ),
    height: 320,
  },
  {
    id: 4,
    content: (
      <Card>
        <CardContent>
          <Typography variant="h6">Tech News</Typography>
          <Typography variant="body2" color="text.secondary">
            Latest updates in the world of technology and innovation.
          </Typography>
        </CardContent>
      </Card>
    ),
    height: 140,
  },
  {
    id: 5,
    content: (
      <Card>
        <CardMedia
          component="img"
          height="160"
          image="https://images.unsplash.com/photo-1518756131217-31eb79b20e8f?w=300"
          alt="Plant"
        />
        <CardContent>
          <Typography variant="h6">Green Life</Typography>
          <Typography variant="body2" color="text.secondary">
            Indoor plants for a better living space
          </Typography>
        </CardContent>
      </Card>
    ),
    height: 280,
  },
  {
    id: 6,
    content: (
      <Card>
        <CardContent>
          <Typography variant="h6">Recipe</Typography>
          <Typography variant="body2" color="text.secondary">
            Quick and easy pasta recipe for busy weekdays. Perfect for dinner!
          </Typography>
        </CardContent>
      </Card>
    ),
    height: 160,
  },
];

export const Default: Story = {
  args: {
    items: basicItems,
    sx: { width: 600 },
  },
};

export const Cards: Story = {
  args: {
    items: cardItems,
    columns: 3,
    spacing: 2,
    sx: { width: 800 },
  },
};

export const ResponsiveColumns: Story = {
  args: {
    items: basicItems,
    columns: { xs: 1, sm: 2, md: 3, lg: 4 },
    spacing: 2,
    sx: { width: '100%', maxWidth: 800 },
  },
};

export const Sequential: Story = {
  args: {
    items: basicItems,
    columns: 3,
    spacing: 2,
    sequential: true,
    sx: { width: 600 },
  },
};

export const DifferentSpacing: Story = {
  render: () => (
    <div>
      <Typography variant="h6" gutterBottom>
        Spacing = 0
      </Typography>
      <MuiMasonry
        items={basicItems.slice(0, 6)}
        columns={3}
        spacing={0}
        sx={{ width: 400, mb: 4 }}
      />
      
      <Typography variant="h6" gutterBottom>
        Spacing = 2
      </Typography>
      <MuiMasonry
        items={basicItems.slice(0, 6)}
        columns={3}
        spacing={2}
        sx={{ width: 400, mb: 4 }}
      />
      
      <Typography variant="h6" gutterBottom>
        Spacing = 4
      </Typography>
      <MuiMasonry
        items={basicItems.slice(0, 6)}
        columns={3}
        spacing={4}
        sx={{ width: 400 }}
      />
    </div>
  ),
};
