import type { Meta, StoryObj } from '@storybook/react';
import { MuiDatePicker } from './MuiDatePicker';
import { useState } from 'react';
import { Stack } from '@mui/material';

const meta: Meta<typeof MuiDatePicker> = {
  title: 'MUI Components/Lab/DatePicker',
  component: MuiDatePicker,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI DatePicker component for selecting dates with calendar interface.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['outlined', 'filled', 'standard'],
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof MuiDatePicker>;

export const Default: Story = {
  args: {
    label: 'Select Date',
  },
};

export const Controlled: Story = {
  render: (args) => {
    const [value, setValue] = useState<Date | null>(new Date());
    
    return (
      <MuiDatePicker
        {...args}
        value={value}
        onChange={(newValue) => setValue(newValue)}
      />
    );
  },
  args: {
    label: 'Controlled Date Picker',
  },
};

export const WithError: Story = {
  args: {
    label: 'Date of Birth',
    error: true,
    helperText: 'Please select a valid date',
  },
};

export const Required: Story = {
  args: {
    label: 'Required Date',
    required: true,
    helperText: 'This field is required',
  },
};

export const Disabled: Story = {
  args: {
    label: 'Disabled Date Picker',
    disabled: true,
    value: new Date(),
  },
};

export const Variants: Story = {
  render: () => (
    <Stack spacing={3} sx={{ width: 300 }}>
      <MuiDatePicker
        label="Outlined (default)"
        variant="outlined"
      />
      <MuiDatePicker
        label="Filled"
        variant="filled"
      />
      <MuiDatePicker
        label="Standard"
        variant="standard"
      />
    </Stack>
  ),
};

export const Sizes: Story = {
  render: () => (
    <Stack spacing={2} sx={{ width: 300 }}>
      <MuiDatePicker
        label="Small"
        size="small"
      />
      <MuiDatePicker
        label="Medium (default)"
        size="medium"
      />
    </Stack>
  ),
};
