import React from 'react';
import { SimpleTreeView } from '@mui/x-tree-view/SimpleTreeView';
import { TreeItem } from '@mui/x-tree-view/TreeItem';
import { Box } from '@mui/material';
import {
  ExpandMore,
  ChevronRight,
  Folder,
  FolderOpen,
  InsertDriveFile,
} from '@mui/icons-material';

export interface TreeNode {
  id: string;
  label: string;
  icon?: React.ReactElement;
  children?: TreeNode[];
  disabled?: boolean;
}

export interface MuiTreeViewProps {
  /**
   * Array of tree nodes
   */
  nodes: TreeNode[];
  /**
   * Selected node IDs
   */
  selectedItems?: string[];
  /**
   * Expanded node IDs
   */
  expandedItems?: string[];
  /**
   * Whether multiple selection is enabled
   */
  multiSelect?: boolean;
  /**
   * Callback when selection changes
   */
  onSelectedItemsChange?: (event: React.SyntheticEvent, itemIds: string[] | string | null) => void;
  /**
   * Callback when expanded items change
   */
  onExpandedItemsChange?: (event: React.SyntheticEvent, itemIds: string[]) => void;
  /**
   * Callback when item is clicked
   */
  onItemClick?: (event: React.SyntheticEvent, itemId: string) => void;
  /**
   * Whether to show default icons
   */
  showDefaultIcons?: boolean;
}

const renderTreeItems = (nodes: TreeNode[], showDefaultIcons: boolean = true): React.ReactNode => {
  return nodes.map((node) => {
    const hasChildren = node.children && node.children.length > 0;
    
    let icon = node.icon;
    if (!icon && showDefaultIcons) {
      if (hasChildren) {
        icon = <Folder />;
      } else {
        icon = <InsertDriveFile />;
      }
    }

    return (
      <TreeItem
        key={node.id}
        itemId={node.id}
        label={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {icon}
            {node.label}
          </Box>
        }
        disabled={node.disabled}
      >
        {hasChildren && renderTreeItems(node.children!, showDefaultIcons)}
      </TreeItem>
    );
  });
};

/**
 * A Material-UI TreeView component for hierarchical data display
 */
export const MuiTreeView: React.FC<MuiTreeViewProps> = ({
  nodes,
  selectedItems,
  expandedItems,
  multiSelect = false,
  onSelectedItemsChange,
  onExpandedItemsChange,
  onItemClick,
  showDefaultIcons = true,
}) => {
  return (
    <SimpleTreeView
      selectedItems={selectedItems}
      expandedItems={expandedItems}
      onSelectedItemsChange={onSelectedItemsChange}
      onExpandedItemsChange={onExpandedItemsChange}
      onItemClick={onItemClick}
      multiSelect={multiSelect}
      slots={{
        expandIcon: ChevronRight,
        collapseIcon: ExpandMore,
      }}
    >
      {renderTreeItems(nodes, showDefaultIcons)}
    </SimpleTreeView>
  );
};
