import React from 'react';
import { Button, ButtonProps } from '@mui/material';

export interface MuiButtonProps extends ButtonProps {
  /**
   * The content of the button
   */
  children: React.ReactNode;
  /**
   * The variant of the button
   */
  variant?: 'text' | 'outlined' | 'contained';
  /**
   * The color of the button
   */
  color?: 'inherit' | 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning';
  /**
   * The size of the button
   */
  size?: 'small' | 'medium' | 'large';
}

/**
 * A Material-UI Button component for user interactions
 */
export const MuiButton: React.FC<MuiButtonProps> = ({
  children,
  variant = 'contained',
  color = 'primary',
  size = 'medium',
  ...props
}) => {
  return (
    <Button
      variant={variant}
      color={color}
      size={size}
      {...props}
    >
      {children}
    </Button>
  );
};
