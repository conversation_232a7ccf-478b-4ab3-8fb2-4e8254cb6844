import React from 'react';
import {
  Card,
  CardContent,
  CardActions,
  CardMedia,
  Typography,
  Button,
  CardProps,
} from '@mui/material';

export interface MuiCardProps extends Omit<CardProps, 'title'> {
  /**
   * The title of the card
   */
  title: string;
  /**
   * The description/content of the card
   */
  description: string;
  /**
   * Optional image URL for the card
   */
  imageUrl?: string;
  /**
   * Optional image alt text
   */
  imageAlt?: string;
  /**
   * Primary action button text
   */
  primaryAction?: string;
  /**
   * Secondary action button text
   */
  secondaryAction?: string;
  /**
   * Callback for primary action
   */
  onPrimaryAction?: () => void;
  /**
   * Callback for secondary action
   */
  onSecondaryAction?: () => void;
}

/**
 * A Material-UI Card component for Storybook demonstration
 */
export const MuiCard: React.FC<MuiCardProps> = ({
  title,
  description,
  imageUrl,
  imageAlt,
  primaryAction,
  secondaryAction,
  onPrimaryAction,
  onSecondaryAction,
  ...props
}) => {
  return (
    <Card {...props} sx={{ maxWidth: 345, ...props.sx }}>
      {imageUrl && (
        <CardMedia
          component="img"
          height="140"
          image={imageUrl}
          alt={imageAlt || title}
        />
      )}
      <CardContent>
        <Typography gutterBottom variant="h5" component="div">
          {title}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {description}
        </Typography>
      </CardContent>
      {(primaryAction || secondaryAction) && (
        <CardActions>
          {secondaryAction && (
            <Button size="small" onClick={onSecondaryAction}>
              {secondaryAction}
            </Button>
          )}
          {primaryAction && (
            <Button size="small" variant="contained" onClick={onPrimaryAction}>
              {primaryAction}
            </Button>
          )}
        </CardActions>
      )}
    </Card>
  );
};
