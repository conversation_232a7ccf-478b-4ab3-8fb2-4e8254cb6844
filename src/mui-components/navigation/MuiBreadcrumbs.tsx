import React from 'react';
import { Breadcrumbs, Link, Typography, BreadcrumbsProps } from '@mui/material';
import { NavigateNext as NavigateNextIcon } from '@mui/icons-material';

export interface BreadcrumbItem {
  label: string;
  href?: string;
  onClick?: () => void;
  disabled?: boolean;
}

export interface MuiBreadcrumbsProps extends BreadcrumbsProps {
  /**
   * Array of breadcrumb items
   */
  items: BreadcrumbItem[];
  /**
   * Custom separator
   */
  separator?: React.ReactNode;
  /**
   * Maximum number of items to display
   */
  maxItems?: number;
  /**
   * Number of items to show after collapse
   */
  itemsAfterCollapse?: number;
  /**
   * Number of items to show before collapse
   */
  itemsBeforeCollapse?: number;
}

/**
 * A Material-UI Breadcrumbs component for navigation hierarchy
 */
export const MuiBreadcrumbs: React.FC<MuiBreadcrumbsProps> = ({
  items,
  separator = <NavigateNextIcon fontSize="small" />,
  maxItems = 8,
  itemsAfterCollapse = 1,
  itemsBeforeCollapse = 1,
  ...props
}) => {
  return (
    <Breadcrumbs
      separator={separator}
      maxItems={maxItems}
      itemsAfterCollapse={itemsAfterCollapse}
      itemsBeforeCollapse={itemsBeforeCollapse}
      {...props}
    >
      {items.map((item, index) => {
        const isLast = index === items.length - 1;
        
        if (isLast) {
          return (
            <Typography key={index} color="text.primary">
              {item.label}
            </Typography>
          );
        }
        
        if (item.href) {
          return (
            <Link
              key={index}
              underline="hover"
              color="inherit"
              href={item.href}
              onClick={item.onClick}
            >
              {item.label}
            </Link>
          );
        }
        
        if (item.onClick) {
          return (
            <Link
              key={index}
              underline="hover"
              color="inherit"
              component="button"
              onClick={item.onClick}
              disabled={item.disabled}
              sx={{ border: 'none', background: 'none', cursor: 'pointer' }}
            >
              {item.label}
            </Link>
          );
        }
        
        return (
          <Typography key={index} color="inherit">
            {item.label}
          </Typography>
        );
      })}
    </Breadcrumbs>
  );
};
