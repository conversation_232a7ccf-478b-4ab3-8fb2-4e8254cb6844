import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { MuiLink } from './MuiLink';
import { Stack, Typography, Box } from '@mui/material';

const meta: Meta<typeof MuiLink> = {
  title: 'MUI Components/Navigation/Link',
  component: MuiLink,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Link component for navigation and external links.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    color: {
      control: { type: 'select' },
      options: ['inherit', 'primary', 'secondary', 'textPrimary', 'textSecondary', 'error'],
    },
    underline: {
      control: { type: 'select' },
      options: ['none', 'hover', 'always'],
    },
    variant: {
      control: { type: 'select' },
      options: ['inherit', 'body1', 'body2', 'subtitle1', 'subtitle2', 'caption', 'button', 'overline', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof MuiLink>;

export const Default: Story = {
  args: {
    children: 'This is a link',
    href: '#',
  },
};

export const Colors: Story = {
  render: () => (
    <Stack spacing={1}>
      <MuiLink href="#" color="primary">Primary link</MuiLink>
      <MuiLink href="#" color="secondary">Secondary link</MuiLink>
      <MuiLink href="#" color="textPrimary">Text primary link</MuiLink>
      <MuiLink href="#" color="textSecondary">Text secondary link</MuiLink>
      <MuiLink href="#" color="error">Error link</MuiLink>
      <MuiLink href="#" color="inherit">Inherit color link</MuiLink>
    </Stack>
  ),
};

export const Underlines: Story = {
  render: () => (
    <Stack spacing={1}>
      <MuiLink href="#" underline="none">No underline</MuiLink>
      <MuiLink href="#" underline="hover">Underline on hover</MuiLink>
      <MuiLink href="#" underline="always">Always underlined</MuiLink>
    </Stack>
  ),
};

export const Variants: Story = {
  render: () => (
    <Stack spacing={1}>
      <MuiLink href="#" variant="h6">Heading 6 link</MuiLink>
      <MuiLink href="#" variant="subtitle1">Subtitle 1 link</MuiLink>
      <MuiLink href="#" variant="body1">Body 1 link</MuiLink>
      <MuiLink href="#" variant="body2">Body 2 link</MuiLink>
      <MuiLink href="#" variant="caption">Caption link</MuiLink>
      <MuiLink href="#" variant="button">Button style link</MuiLink>
    </Stack>
  ),
};

export const External: Story = {
  args: {
    children: 'External link (opens in new tab)',
    href: 'https://mui.com',
    external: true,
  },
};

export const WithClickHandler: Story = {
  args: {
    children: 'Click me',
    onClick: () => alert('Link clicked!'),
  },
};

export const Disabled: Story = {
  args: {
    children: 'Disabled link',
    href: '#',
    disabled: true,
  },
};

export const InText: Story = {
  render: () => (
    <Typography variant="body1">
      This is a paragraph with an{' '}
      <MuiLink href="#" color="primary">
        inline link
      </MuiLink>{' '}
      that flows naturally with the text. You can also have{' '}
      <MuiLink href="https://mui.com" external color="secondary">
        external links
      </MuiLink>{' '}
      that open in a new tab.
    </Typography>
  ),
};

export const Navigation: Story = {
  render: () => (
    <Box sx={{ p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
      <Typography variant="h6" gutterBottom>
        Navigation Menu
      </Typography>
      <Stack direction="row" spacing={3}>
        <MuiLink href="#" underline="none">Home</MuiLink>
        <MuiLink href="#" underline="none">About</MuiLink>
        <MuiLink href="#" underline="none">Services</MuiLink>
        <MuiLink href="#" underline="none">Contact</MuiLink>
      </Stack>
    </Box>
  ),
};
