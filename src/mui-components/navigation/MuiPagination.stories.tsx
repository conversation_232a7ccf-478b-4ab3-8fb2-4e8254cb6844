import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MuiPagination } from './MuiPagination';
import { useState } from 'react';
import { Stack, Typography } from '@mui/material';

const meta: Meta<typeof MuiPagination> = {
  title: 'MUI Components/Navigation/Pagination',
  component: MuiPagination,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Pagination component for navigating through pages of content.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['text', 'outlined'],
    },
    shape: {
      control: { type: 'select' },
      options: ['circular', 'rounded'],
    },
    color: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'standard'],
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof MuiPagination>;

export const Default: Story = {
  args: {
    count: 10,
    page: 1,
  },
};

export const Controlled: Story = {
  render: (args) => {
    const [page, setPage] = useState(1);
    
    return (
      <Stack spacing={2} alignItems="center">
        <Typography>Current page: {page}</Typography>
        <MuiPagination
          {...args}
          page={page}
          onChange={(event, newPage) => setPage(newPage)}
        />
      </Stack>
    );
  },
  args: {
    count: 10,
  },
};

export const Variants: Story = {
  render: () => (
    <Stack spacing={3} alignItems="center">
      <div>
        <Typography variant="h6" gutterBottom>Text (default)</Typography>
        <MuiPagination count={10} variant="text" />
      </div>
      
      <div>
        <Typography variant="h6" gutterBottom>Outlined</Typography>
        <MuiPagination count={10} variant="outlined" />
      </div>
    </Stack>
  ),
};

export const Shapes: Story = {
  render: () => (
    <Stack spacing={3} alignItems="center">
      <div>
        <Typography variant="h6" gutterBottom>Circular (default)</Typography>
        <MuiPagination count={10} shape="circular" />
      </div>
      
      <div>
        <Typography variant="h6" gutterBottom>Rounded</Typography>
        <MuiPagination count={10} shape="rounded" />
      </div>
    </Stack>
  ),
};

export const Colors: Story = {
  render: () => (
    <Stack spacing={3} alignItems="center">
      <div>
        <Typography variant="h6" gutterBottom>Primary (default)</Typography>
        <MuiPagination count={10} color="primary" />
      </div>
      
      <div>
        <Typography variant="h6" gutterBottom>Secondary</Typography>
        <MuiPagination count={10} color="secondary" />
      </div>
      
      <div>
        <Typography variant="h6" gutterBottom>Standard</Typography>
        <MuiPagination count={10} color="standard" />
      </div>
    </Stack>
  ),
};

export const Sizes: Story = {
  render: () => (
    <Stack spacing={3} alignItems="center">
      <div>
        <Typography variant="h6" gutterBottom>Small</Typography>
        <MuiPagination count={10} size="small" />
      </div>
      
      <div>
        <Typography variant="h6" gutterBottom>Medium (default)</Typography>
        <MuiPagination count={10} size="medium" />
      </div>
      
      <div>
        <Typography variant="h6" gutterBottom>Large</Typography>
        <MuiPagination count={10} size="large" />
      </div>
    </Stack>
  ),
};

export const WithFirstLast: Story = {
  args: {
    count: 20,
    showFirstButton: true,
    showLastButton: true,
  },
};

export const CustomBoundaries: Story = {
  render: () => (
    <Stack spacing={3} alignItems="center">
      <div>
        <Typography variant="h6" gutterBottom>Default (boundaryCount=1, siblingCount=1)</Typography>
        <MuiPagination count={20} page={10} />
      </div>
      
      <div>
        <Typography variant="h6" gutterBottom>More boundaries (boundaryCount=2)</Typography>
        <MuiPagination count={20} page={10} boundaryCount={2} />
      </div>
      
      <div>
        <Typography variant="h6" gutterBottom>More siblings (siblingCount=2)</Typography>
        <MuiPagination count={20} page={10} siblingCount={2} />
      </div>
    </Stack>
  ),
};

export const Disabled: Story = {
  args: {
    count: 10,
    disabled: true,
  },
};
