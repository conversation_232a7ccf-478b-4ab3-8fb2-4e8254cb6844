import React from 'react';
import { Tabs, Tab, Box, TabsProps } from '@mui/material';

export interface TabItem {
  label: string;
  value: string | number;
  disabled?: boolean;
  icon?: React.ReactElement;
}

export interface MuiTabsProps extends Omit<TabsProps, 'children'> {
  /**
   * Array of tab items
   */
  tabs: TabItem[];
  /**
   * Current active tab value
   */
  value: string | number;
  /**
   * Callback when tab changes
   */
  onChange: (event: React.SyntheticEvent, newValue: string | number) => void;
  /**
   * Tab content for each tab
   */
  tabPanels?: { [key: string | number]: React.ReactNode };
  /**
   * Whether to show tab panels
   */
  showPanels?: boolean;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: string | number;
  value: string | number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

/**
 * A Material-UI Tabs component for organizing content into tabs
 */
export const MuiTabs: React.FC<MuiTabsProps> = ({
  tabs,
  value,
  onChange,
  tabPanels,
  showPanels = true,
  ...props
}) => {
  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={value} onChange={onChange} {...props}>
          {tabs.map((tab) => (
            <Tab
              key={tab.value}
              label={tab.label}
              value={tab.value}
              disabled={tab.disabled}
              icon={tab.icon}
              id={`tab-${tab.value}`}
              aria-controls={`tabpanel-${tab.value}`}
            />
          ))}
        </Tabs>
      </Box>
      
      {showPanels && tabPanels && (
        <>
          {tabs.map((tab) => (
            <TabPanel key={tab.value} value={value} index={tab.value}>
              {tabPanels[tab.value]}
            </TabPanel>
          ))}
        </>
      )}
    </Box>
  );
};
