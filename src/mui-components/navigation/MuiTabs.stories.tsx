import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MuiTabs } from './MuiTabs';
import { useState } from 'react';
import { Typography } from '@mui/material';
import PhoneIcon from '@mui/icons-material/Phone';
import FavoriteIcon from '@mui/icons-material/Favorite';
import PersonPinIcon from '@mui/icons-material/PersonPin';

const meta: Meta<typeof MuiTabs> = {
  title: 'MUI Components/Navigation/Tabs',
  component: MuiTabs,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Tabs component for organizing content into tabbed sections.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    orientation: {
      control: { type: 'select' },
      options: ['horizontal', 'vertical'],
    },
    variant: {
      control: { type: 'select' },
      options: ['standard', 'scrollable', 'fullWidth'],
    },
    indicatorColor: {
      control: { type: 'select' },
      options: ['primary', 'secondary'],
    },
    textColor: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'inherit'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const basicTabs = [
  { label: 'Tab One', value: 'one' },
  { label: 'Tab Two', value: 'two' },
  { label: 'Tab Three', value: 'three' },
];

const basicTabPanels = {
  one: <Typography>Content for Tab One</Typography>,
  two: <Typography>Content for Tab Two</Typography>,
  three: <Typography>Content for Tab Three</Typography>,
};

export const Default: Story = {
  render: (args) => {
    const [value, setValue] = useState('one');
    
    return (
      <MuiTabs
        {...args}
        value={value}
        onChange={(event, newValue) => setValue(newValue)}
      />
    );
  },
  args: {
    tabs: basicTabs,
    tabPanels: basicTabPanels,
  },
};

export const WithIcons: Story = {
  render: (args) => {
    const [value, setValue] = useState('phone');
    
    return (
      <MuiTabs
        {...args}
        value={value}
        onChange={(event, newValue) => setValue(newValue)}
      />
    );
  },
  args: {
    tabs: [
      { label: 'Phone', value: 'phone', icon: <PhoneIcon /> },
      { label: 'Favorites', value: 'favorites', icon: <FavoriteIcon /> },
      { label: 'Nearby', value: 'nearby', icon: <PersonPinIcon /> },
    ],
    tabPanels: {
      phone: <Typography>Phone contacts and call history</Typography>,
      favorites: <Typography>Your favorite contacts</Typography>,
      nearby: <Typography>People nearby</Typography>,
    },
  },
};

export const Scrollable: Story = {
  render: (args) => {
    const [value, setValue] = useState('item1');
    
    return (
      <MuiTabs
        {...args}
        value={value}
        onChange={(event, newValue) => setValue(newValue)}
      />
    );
  },
  args: {
    tabs: Array.from({ length: 10 }, (_, i) => ({
      label: `Item ${i + 1}`,
      value: `item${i + 1}`,
    })),
    variant: 'scrollable',
    scrollButtons: 'auto',
    tabPanels: Object.fromEntries(
      Array.from({ length: 10 }, (_, i) => [
        `item${i + 1}`,
        <Typography key={i}>Content for Item {i + 1}</Typography>,
      ])
    ),
  },
};

export const Vertical: Story = {
  render: (args) => {
    const [value, setValue] = useState('one');
    
    return (
      <div style={{ height: 400, display: 'flex' }}>
        <MuiTabs
          {...args}
          value={value}
          onChange={(event, newValue) => setValue(newValue)}
        />
      </div>
    );
  },
  args: {
    tabs: basicTabs,
    tabPanels: basicTabPanels,
    orientation: 'vertical',
  },
};

export const WithDisabledTab: Story = {
  render: (args) => {
    const [value, setValue] = useState('one');
    
    return (
      <MuiTabs
        {...args}
        value={value}
        onChange={(event, newValue) => setValue(newValue)}
      />
    );
  },
  args: {
    tabs: [
      { label: 'Active Tab', value: 'one' },
      { label: 'Disabled Tab', value: 'two', disabled: true },
      { label: 'Another Active Tab', value: 'three' },
    ],
    tabPanels: {
      one: <Typography>Content for Active Tab</Typography>,
      two: <Typography>This content is not accessible</Typography>,
      three: <Typography>Content for Another Active Tab</Typography>,
    },
  },
};
