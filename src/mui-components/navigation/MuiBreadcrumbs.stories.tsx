import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MuiBreadcrumbs } from './MuiBreadcrumbs';
import { Home, Grain } from '@mui/icons-material';

const meta: Meta<typeof MuiBreadcrumbs> = {
  title: 'MUI Components/Navigation/Breadcrumbs',
  component: MuiBreadcrumbs,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Breadcrumbs component for showing navigation hierarchy.',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof MuiBreadcrumbs>;

const basicItems = [
  { label: 'Home', href: '/' },
  { label: 'Products', href: '/products' },
  { label: 'Electronics', href: '/products/electronics' },
  { label: 'Smartphones' },
];

export const Default: Story = {
  args: {
    items: basicItems,
  },
};

export const WithClickHandlers: Story = {
  args: {
    items: [
      { label: 'Home', onClick: () => console.log('Navigate to Home') },
      { label: 'Category', onClick: () => console.log('Navigate to Category') },
      { label: 'Subcategory', onClick: () => console.log('Navigate to Subcategory') },
      { label: 'Current Page' },
    ],
  },
};

export const CustomSeparator: Story = {
  args: {
    items: basicItems,
    separator: '›',
  },
};

export const WithIcons: Story = {
  args: {
    items: [
      { label: 'Home', href: '/', icon: <Home fontSize="small" /> },
      { label: 'Catalog', href: '/catalog' },
      { label: 'Accessories', href: '/catalog/accessories' },
      { label: 'Belts' },
    ],
  },
};

export const Collapsed: Story = {
  args: {
    items: [
      { label: 'Home', href: '/' },
      { label: 'Level 1', href: '/level1' },
      { label: 'Level 2', href: '/level1/level2' },
      { label: 'Level 3', href: '/level1/level2/level3' },
      { label: 'Level 4', href: '/level1/level2/level3/level4' },
      { label: 'Level 5', href: '/level1/level2/level3/level4/level5' },
      { label: 'Current Page' },
    ],
    maxItems: 4,
    itemsAfterCollapse: 2,
    itemsBeforeCollapse: 1,
  },
};
