import React from 'react';
import { Link, LinkProps } from '@mui/material';

export interface MuiLinkProps extends LinkProps {
  /**
   * The content of the link
   */
  children: React.ReactNode;
  /**
   * The URL to link to
   */
  href?: string;
  /**
   * The color of the link
   */
  color?: 'inherit' | 'primary' | 'secondary' | 'textPrimary' | 'textSecondary' | 'error';
  /**
   * The underline behavior
   */
  underline?: 'none' | 'hover' | 'always';
  /**
   * The variant of the link
   */
  variant?: 'inherit' | 'body1' | 'body2' | 'subtitle1' | 'subtitle2' | 'caption' | 'button' | 'overline' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  /**
   * Whether the link should open in a new tab
   */
  external?: boolean;
  /**
   * Whether the link is disabled
   */
  disabled?: boolean;
  /**
   * Click handler
   */
  onClick?: (event: React.MouseEvent<HTMLAnchorElement>) => void;
}

/**
 * A Material-UI Link component for navigation and external links
 */
export const MuiLink: React.FC<MuiLinkProps> = ({
  children,
  href,
  color = 'primary',
  underline = 'hover',
  variant = 'inherit',
  external = false,
  disabled = false,
  onClick,
  ...props
}) => {
  const linkProps = {
    ...(external && {
      target: '_blank',
      rel: 'noopener noreferrer',
    }),
    ...(disabled && {
      'aria-disabled': true,
      style: { pointerEvents: 'none', opacity: 0.5 },
    }),
  };

  return (
    <Link
      href={disabled ? undefined : href}
      color={color}
      underline={underline}
      variant={variant}
      onClick={disabled ? undefined : onClick}
      {...linkProps}
      {...props}
    >
      {children}
    </Link>
  );
};
