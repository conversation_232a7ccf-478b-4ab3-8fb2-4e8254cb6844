import React from 'react';
import { Pagination, PaginationProps } from '@mui/material';

export interface MuiPaginationProps extends PaginationProps {
  /**
   * The current page number
   */
  page?: number;
  /**
   * The total number of pages
   */
  count: number;
  /**
   * Callback when page changes
   */
  onChange?: (event: React.ChangeEvent<unknown>, page: number) => void;
  /**
   * The variant of the pagination
   */
  variant?: 'text' | 'outlined';
  /**
   * The shape of the pagination items
   */
  shape?: 'circular' | 'rounded';
  /**
   * The color of the pagination
   */
  color?: 'primary' | 'secondary' | 'standard';
  /**
   * The size of the pagination
   */
  size?: 'small' | 'medium' | 'large';
  /**
   * Whether to show first and last page buttons
   */
  showFirstButton?: boolean;
  /**
   * Whether to show last page button
   */
  showLastButton?: boolean;
  /**
   * Number of siblings on each side of current page
   */
  siblingCount?: number;
  /**
   * Number of pages at the beginning and end
   */
  boundaryCount?: number;
  /**
   * Whether the pagination is disabled
   */
  disabled?: boolean;
}

/**
 * A Material-UI Pagination component for navigating through pages
 */
export const MuiPagination: React.FC<MuiPaginationProps> = ({
  page = 1,
  count,
  onChange,
  variant = 'text',
  shape = 'circular',
  color = 'primary',
  size = 'medium',
  showFirstButton = false,
  showLastButton = false,
  siblingCount = 1,
  boundaryCount = 1,
  disabled = false,
  ...props
}) => {
  return (
    <Pagination
      page={page}
      count={count}
      onChange={onChange}
      variant={variant}
      shape={shape}
      color={color}
      size={size}
      showFirstButton={showFirstButton}
      showLastButton={showLastButton}
      siblingCount={siblingCount}
      boundaryCount={boundaryCount}
      disabled={disabled}
      {...props}
    />
  );
};
