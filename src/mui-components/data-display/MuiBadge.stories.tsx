import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MuiBadge } from './MuiBadge';
import { IconButton } from '@mui/material';
import MailIcon from '@mui/icons-material/Mail';
import NotificationsIcon from '@mui/icons-material/Notifications';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';

const meta: Meta<typeof MuiBadge> = {
  title: 'MUI Components/Data Display/Badge',
  component: MuiBadge,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Badge component for displaying notifications and status indicators.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    color: {
      control: { type: 'select' },
      options: ['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning'],
    },
    variant: {
      control: { type: 'select' },
      options: ['standard', 'dot'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    badgeContent: 4,
    children: (
      <IconButton>
        <MailIcon />
      </IconButton>
    ),
  },
};

export const DotBadge: Story = {
  args: {
    variant: 'dot',
    color: 'secondary',
    children: (
      <IconButton>
        <NotificationsIcon />
      </IconButton>
    ),
  },
};

export const MaxCount: Story = {
  args: {
    badgeContent: 1000,
    max: 99,
    children: (
      <IconButton>
        <MailIcon />
      </IconButton>
    ),
  },
};

export const Colors: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '24px', alignItems: 'center' }}>
      <MuiBadge badgeContent={4} color="primary">
        <IconButton><MailIcon /></IconButton>
      </MuiBadge>
      <MuiBadge badgeContent={4} color="secondary">
        <IconButton><MailIcon /></IconButton>
      </MuiBadge>
      <MuiBadge badgeContent={4} color="error">
        <IconButton><MailIcon /></IconButton>
      </MuiBadge>
      <MuiBadge badgeContent={4} color="success">
        <IconButton><MailIcon /></IconButton>
      </MuiBadge>
      <MuiBadge badgeContent={4} color="warning">
        <IconButton><MailIcon /></IconButton>
      </MuiBadge>
    </div>
  ),
};

export const Positions: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '24px', alignItems: 'center' }}>
      <MuiBadge 
        badgeContent={4} 
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <IconButton><ShoppingCartIcon /></IconButton>
      </MuiBadge>
      <MuiBadge 
        badgeContent={4} 
        anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
      >
        <IconButton><ShoppingCartIcon /></IconButton>
      </MuiBadge>
      <MuiBadge 
        badgeContent={4} 
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <IconButton><ShoppingCartIcon /></IconButton>
      </MuiBadge>
      <MuiBadge 
        badgeContent={4} 
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
      >
        <IconButton><ShoppingCartIcon /></IconButton>
      </MuiBadge>
    </div>
  ),
};

export const Invisible: Story = {
  args: {
    badgeContent: 0,
    showZero: false,
    children: (
      <IconButton>
        <MailIcon />
      </IconButton>
    ),
  },
};
