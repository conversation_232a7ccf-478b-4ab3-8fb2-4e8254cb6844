import React from 'react';
import { SvgIconProps } from '@mui/material';
import {
  Home,
  Person,
  Settings,
  Search,
  Favorite,
  Star,
  Delete,
  Edit,
  Add,
  Remove,
  Check,
  Close,
  ArrowBack,
  ArrowForward,
  ExpandMore,
  ExpandLess,
  Menu,
  MoreVert,
  Notifications,
  Email,
  Phone,
  LocationOn,
  DateRange,
  AccessTime,
  Visibility,
  VisibilityOff,
  Download,
  Upload,
  Share,
  Print,
  Save,
  Refresh,
  Info,
  Warning,
  Error,
  CheckCircle,
} from '@mui/icons-material';

export interface MuiIconProps extends SvgIconProps {
  /**
   * The name of the icon to display
   */
  name: IconName;
  /**
   * The size of the icon
   */
  size?: 'small' | 'medium' | 'large' | 'inherit';
  /**
   * The color of the icon
   */
  color?: 'inherit' | 'action' | 'disabled' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
}

export type IconName = 
  | 'home' | 'person' | 'settings' | 'search' | 'favorite' | 'star'
  | 'delete' | 'edit' | 'add' | 'remove' | 'check' | 'close'
  | 'arrowBack' | 'arrowForward' | 'expandMore' | 'expandLess'
  | 'menu' | 'moreVert' | 'notifications' | 'email' | 'phone'
  | 'locationOn' | 'dateRange' | 'accessTime' | 'visibility' | 'visibilityOff'
  | 'download' | 'upload' | 'share' | 'print' | 'save' | 'refresh'
  | 'info' | 'warning' | 'error' | 'checkCircle';

const iconMap: Record<IconName, React.ComponentType<SvgIconProps>> = {
  home: Home,
  person: Person,
  settings: Settings,
  search: Search,
  favorite: Favorite,
  star: Star,
  delete: Delete,
  edit: Edit,
  add: Add,
  remove: Remove,
  check: Check,
  close: Close,
  arrowBack: ArrowBack,
  arrowForward: ArrowForward,
  expandMore: ExpandMore,
  expandLess: ExpandLess,
  menu: Menu,
  moreVert: MoreVert,
  notifications: Notifications,
  email: Email,
  phone: Phone,
  locationOn: LocationOn,
  dateRange: DateRange,
  accessTime: AccessTime,
  visibility: Visibility,
  visibilityOff: VisibilityOff,
  download: Download,
  upload: Upload,
  share: Share,
  print: Print,
  save: Save,
  refresh: Refresh,
  info: Info,
  warning: Warning,
  error: Error,
  checkCircle: CheckCircle,
};

/**
 * A Material-UI Icons component for displaying Material Design icons
 */
export const MuiIcon: React.FC<MuiIconProps> = ({
  name,
  size = 'medium',
  color = 'inherit',
  ...props
}) => {
  const IconComponent = iconMap[name];

  if (!IconComponent) {
    console.warn(`Icon "${name}" not found in iconMap`);
    return null;
  }

  const fontSize = size === 'inherit' ? 'inherit' : size;

  return (
    <IconComponent
      fontSize={fontSize}
      color={color}
      {...props}
    />
  );
};

// Export commonly used icons for direct import
export {
  Home,
  Person,
  Settings,
  Search,
  Favorite,
  Star,
  Delete,
  Edit,
  Add,
  Remove,
  Check,
  Close,
  ArrowBack,
  ArrowForward,
  ExpandMore,
  ExpandLess,
  Menu,
  MoreVert,
  Notifications,
  Email,
  Phone,
  LocationOn,
  DateRange,
  AccessTime,
  Visibility,
  VisibilityOff,
  Download,
  Upload,
  Share,
  Print,
  Save,
  Refresh,
  Info,
  Warning,
  Error,
  CheckCircle,
};
