import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { MuiTooltip } from './MuiTooltip';
import { useState } from 'react';
import { 
  Button, 
  IconButton, 
  Typography, 
  Box, 
  Grid, 
  Fab,
  Stack,
} from '@mui/material';
import {
  Delete,
  Add,
  Info,
  Help,
  Settings,
  Favorite,
} from '@mui/icons-material';

const meta: Meta<typeof MuiTooltip> = {
  title: 'MUI Components/Data Display/Tooltip',
  component: MuiTooltip,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Tooltip component for displaying helpful information on hover or focus.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    placement: {
      control: { type: 'select' },
      options: [
        'bottom-end', 'bottom-start', 'bottom',
        'left-end', 'left-start', 'left',
        'right-end', 'right-start', 'right',
        'top-end', 'top-start', 'top'
      ],
    },
  },
};

export default meta;
type Story = StoryObj<typeof MuiTooltip>;

export const Default: Story = {
  args: {
    title: 'This is a tooltip',
    children: <Button variant="outlined">Hover me</Button>,
  },
};

export const WithArrow: Story = {
  args: {
    title: 'Tooltip with arrow',
    arrow: true,
    children: <Button variant="contained">Hover for arrow tooltip</Button>,
  },
};

export const Placements: Story = {
  render: () => (
    <Box sx={{ width: 500, height: 300, position: 'relative' }}>
      <Grid container spacing={1} justifyContent="center">
        <Grid item>
          <MuiTooltip title="Top Start" placement="top-start" arrow>
            <Button>TS</Button>
          </MuiTooltip>
        </Grid>
        <Grid item>
          <MuiTooltip title="Top" placement="top" arrow>
            <Button>T</Button>
          </MuiTooltip>
        </Grid>
        <Grid item>
          <MuiTooltip title="Top End" placement="top-end" arrow>
            <Button>TE</Button>
          </MuiTooltip>
        </Grid>
      </Grid>
      
      <Grid container spacing={1} justifyContent="space-between" sx={{ mt: 2 }}>
        <Grid item>
          <Stack spacing={1}>
            <MuiTooltip title="Left Start" placement="left-start" arrow>
              <Button>LS</Button>
            </MuiTooltip>
            <MuiTooltip title="Left" placement="left" arrow>
              <Button>L</Button>
            </MuiTooltip>
            <MuiTooltip title="Left End" placement="left-end" arrow>
              <Button>LE</Button>
            </MuiTooltip>
          </Stack>
        </Grid>
        
        <Grid item>
          <Stack spacing={1}>
            <MuiTooltip title="Right Start" placement="right-start" arrow>
              <Button>RS</Button>
            </MuiTooltip>
            <MuiTooltip title="Right" placement="right" arrow>
              <Button>R</Button>
            </MuiTooltip>
            <MuiTooltip title="Right End" placement="right-end" arrow>
              <Button>RE</Button>
            </MuiTooltip>
          </Stack>
        </Grid>
      </Grid>
      
      <Grid container spacing={1} justifyContent="center" sx={{ mt: 2 }}>
        <Grid item>
          <MuiTooltip title="Bottom Start" placement="bottom-start" arrow>
            <Button>BS</Button>
          </MuiTooltip>
        </Grid>
        <Grid item>
          <MuiTooltip title="Bottom" placement="bottom" arrow>
            <Button>B</Button>
          </MuiTooltip>
        </Grid>
        <Grid item>
          <MuiTooltip title="Bottom End" placement="bottom-end" arrow>
            <Button>BE</Button>
          </MuiTooltip>
        </Grid>
      </Grid>
    </Box>
  ),
};

export const WithIcons: Story = {
  render: () => (
    <Stack direction="row" spacing={2}>
      <MuiTooltip title="Delete item">
        <IconButton color="error">
          <Delete />
        </IconButton>
      </MuiTooltip>
      
      <MuiTooltip title="Add new item">
        <IconButton color="primary">
          <Add />
        </IconButton>
      </MuiTooltip>
      
      <MuiTooltip title="More information">
        <IconButton color="info">
          <Info />
        </IconButton>
      </MuiTooltip>
      
      <MuiTooltip title="Help and support">
        <IconButton>
          <Help />
        </IconButton>
      </MuiTooltip>
    </Stack>
  ),
};

export const Interactive: Story = {
  render: () => (
    <MuiTooltip
      title={
        <Box>
          <Typography variant="subtitle2">Interactive Tooltip</Typography>
          <Typography variant="body2">
            You can interact with this tooltip content.
          </Typography>
          <Button size="small" sx={{ mt: 1 }}>
            Click me
          </Button>
        </Box>
      }
      interactive
      arrow
    >
      <Button variant="outlined">Hover for interactive tooltip</Button>
    </MuiTooltip>
  ),
};

export const Controlled: Story = {
  render: () => {
    const [open, setOpen] = useState(false);
    
    return (
      <Stack spacing={2} alignItems="center">
        <Button
          variant="outlined"
          onClick={() => setOpen(!open)}
        >
          {open ? 'Hide' : 'Show'} Tooltip
        </Button>
        
        <MuiTooltip
          title="This is a controlled tooltip"
          open={open}
          arrow
        >
          <Button variant="contained">Target Element</Button>
        </MuiTooltip>
      </Stack>
    );
  },
};

export const WithDelay: Story = {
  render: () => (
    <Stack direction="row" spacing={2}>
      <MuiTooltip title="No delay" enterDelay={0} leaveDelay={0}>
        <Button variant="outlined">No Delay</Button>
      </MuiTooltip>
      
      <MuiTooltip title="500ms enter delay" enterDelay={500}>
        <Button variant="outlined">Slow Enter</Button>
      </MuiTooltip>
      
      <MuiTooltip title="500ms leave delay" leaveDelay={500}>
        <Button variant="outlined">Slow Leave</Button>
      </MuiTooltip>
    </Stack>
  ),
};

export const FollowCursor: Story = {
  render: () => (
    <MuiTooltip title="This tooltip follows your cursor" followCursor>
      <Box
        sx={{
          width: 200,
          height: 100,
          backgroundColor: 'primary.light',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: 1,
          cursor: 'pointer',
        }}
      >
        <Typography color="white">Move cursor here</Typography>
      </Box>
    </MuiTooltip>
  ),
};

export const Disabled: Story = {
  render: () => (
    <Stack direction="row" spacing={2}>
      <MuiTooltip title="This tooltip is enabled">
        <Button variant="outlined">Enabled Tooltip</Button>
      </MuiTooltip>
      
      <MuiTooltip title="This tooltip is disabled" disabled>
        <Button variant="outlined">Disabled Tooltip</Button>
      </MuiTooltip>
    </Stack>
  ),
};

export const FloatingActionButton: Story = {
  render: () => (
    <Stack direction="row" spacing={2}>
      <MuiTooltip title="Add to favorites" placement="top">
        <Fab color="primary" size="small">
          <Favorite />
        </Fab>
      </MuiTooltip>
      
      <MuiTooltip title="Settings" placement="top">
        <Fab color="secondary" size="small">
          <Settings />
        </Fab>
      </MuiTooltip>
      
      <MuiTooltip title="Add new item" placement="top">
        <Fab color="success" size="small">
          <Add />
        </Fab>
      </MuiTooltip>
    </Stack>
  ),
};
