import React from 'react';
import { Typography, TypographyProps } from '@mui/material';

export interface MuiTypographyProps extends TypographyProps {
  /**
   * The content of the typography
   */
  children: React.ReactNode;
  /**
   * The variant of the typography
   */
  variant?: 
    | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
    | 'subtitle1' | 'subtitle2'
    | 'body1' | 'body2'
    | 'caption' | 'button' | 'overline'
    | 'inherit';
  /**
   * The color of the text
   */
  color?: 
    | 'inherit' | 'primary' | 'secondary' | 'textPrimary' | 'textSecondary'
    | 'error' | 'warning' | 'info' | 'success';
  /**
   * The alignment of the text
   */
  align?: 'inherit' | 'left' | 'center' | 'right' | 'justify';
  /**
   * Whether to add bottom margin
   */
  gutterBottom?: boolean;
  /**
   * Whether to add top margin
   */
  gutterTop?: boolean;
  /**
   * Whether to make text bold
   */
  bold?: boolean;
  /**
   * Whether to make text italic
   */
  italic?: boolean;
  /**
   * Whether to underline text
   */
  underline?: boolean;
  /**
   * Whether to truncate text with ellipsis
   */
  noWrap?: boolean;
  /**
   * Whether to make text uppercase
   */
  uppercase?: boolean;
  /**
   * Whether to make text lowercase
   */
  lowercase?: boolean;
  /**
   * Whether to capitalize text
   */
  capitalize?: boolean;
}

/**
 * A Material-UI Typography component for displaying text with consistent styling
 */
export const MuiTypography: React.FC<MuiTypographyProps> = ({
  children,
  variant = 'body1',
  color = 'inherit',
  align = 'inherit',
  gutterBottom = false,
  gutterTop = false,
  bold = false,
  italic = false,
  underline = false,
  noWrap = false,
  uppercase = false,
  lowercase = false,
  capitalize = false,
  sx,
  ...props
}) => {
  const getTextTransform = () => {
    if (uppercase) return 'uppercase';
    if (lowercase) return 'lowercase';
    if (capitalize) return 'capitalize';
    return 'none';
  };

  const customSx = {
    fontWeight: bold ? 'bold' : 'inherit',
    fontStyle: italic ? 'italic' : 'inherit',
    textDecoration: underline ? 'underline' : 'inherit',
    textTransform: getTextTransform(),
    marginTop: gutterTop ? 1 : 0,
    ...sx,
  };

  return (
    <Typography
      variant={variant}
      color={color}
      align={align}
      gutterBottom={gutterBottom}
      noWrap={noWrap}
      sx={customSx}
      {...props}
    >
      {children}
    </Typography>
  );
};
