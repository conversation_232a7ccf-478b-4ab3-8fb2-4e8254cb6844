import type { Meta, StoryObj } from '@storybook/react';
import { MuiAvatar } from './MuiAvatar';
import PersonIcon from '@mui/icons-material/Person';
import { AvatarGroup } from '@mui/material';

const meta: Meta<typeof MuiAvatar> = {
  title: 'MUI Components/Data Display/Avatar',
  component: MuiAvatar,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Avatar component for displaying user profile pictures or initials.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    name: '<PERSON>',
  },
};

export const WithImage: Story = {
  args: {
    name: '<PERSON>',
    src: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80',
    alt: '<PERSON> Smith',
  },
};

export const WithIcon: Story = {
  args: {
    children: <PersonIcon />,
  },
};

export const Sizes: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
      <MuiAvatar name="Small" size="small" />
      <MuiAvatar name="Medium" size="medium" />
      <MuiAvatar name="Large" size="large" />
      <MuiAvatar name="Custom" size={80} />
    </div>
  ),
};

export const Colors: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
      <MuiAvatar name="Primary" sx={{ bgcolor: 'primary.main' }} />
      <MuiAvatar name="Secondary" sx={{ bgcolor: 'secondary.main' }} />
      <MuiAvatar name="Success" sx={{ bgcolor: 'success.main' }} />
      <MuiAvatar name="Error" sx={{ bgcolor: 'error.main' }} />
      <MuiAvatar name="Warning" sx={{ bgcolor: 'warning.main' }} />
    </div>
  ),
};

export const AvatarGroupExample: Story = {
  render: () => (
    <AvatarGroup max={4}>
      <MuiAvatar name="Alice Johnson" />
      <MuiAvatar name="Bob Smith" />
      <MuiAvatar name="Charlie Brown" />
      <MuiAvatar name="Diana Prince" />
      <MuiAvatar name="Eve Wilson" />
      <MuiAvatar name="Frank Miller" />
    </AvatarGroup>
  ),
};
