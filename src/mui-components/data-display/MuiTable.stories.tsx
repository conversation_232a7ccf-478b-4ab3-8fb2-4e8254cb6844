import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MuiTable } from './MuiTable';
import { Chip, Avatar } from '@mui/material';

const meta: Meta<typeof MuiTable> = {
  title: 'MUI Components/Data Display/Table',
  component: MuiTable,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'A Material-UI Table component for displaying tabular data with sorting, selection, and pagination capabilities.',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof MuiTable>;

const basicColumns = [
  { id: 'name', label: 'Name', minWidth: 170 },
  { id: 'code', label: 'ISO Code', minWidth: 100 },
  { id: 'population', label: 'Population', minWidth: 170, align: 'right' as const },
  { id: 'size', label: 'Size (km²)', minWidth: 170, align: 'right' as const },
];

const basicRows = [
  { id: 1, name: 'India', code: 'IN', population: 1324171354, size: 3287263 },
  { id: 2, name: 'China', code: 'CN', population: 1403500365, size: 9596961 },
  { id: 3, name: 'Italy', code: 'IT', population: 60483973, size: 301340 },
  { id: 4, name: 'United States', code: 'US', population: *********, size: 9833520 },
  { id: 5, name: 'Canada', code: 'CA', population: 37602103, size: 9984670 },
  { id: 6, name: 'Australia', code: 'AU', population: 25475400, size: 7692024 },
  { id: 7, name: 'Germany', code: 'DE', population: 83019200, size: 357578 },
  { id: 8, name: 'Ireland', code: 'IE', population: 4857000, size: 70273 },
  { id: 9, name: 'Mexico', code: 'MX', population: *********, size: 1972550 },
  { id: 10, name: 'Japan', code: 'JP', population: *********, size: 377973 },
];

const employeeColumns = [
  { id: 'avatar', label: '', minWidth: 50, sortable: false },
  { id: 'name', label: 'Name', minWidth: 150 },
  { id: 'email', label: 'Email', minWidth: 200 },
  { id: 'department', label: 'Department', minWidth: 120 },
  { id: 'salary', label: 'Salary', minWidth: 100, align: 'right' as const, format: (value: number) => `$${value.toLocaleString()}` },
  { id: 'status', label: 'Status', minWidth: 100 },
];

const employeeRows = [
  {
    id: 1,
    avatar: <Avatar>JD</Avatar>,
    name: 'John Doe',
    email: '<EMAIL>',
    department: 'Engineering',
    salary: 85000,
    status: <Chip label="Active" color="success" size="small" />,
  },
  {
    id: 2,
    avatar: <Avatar>JS</Avatar>,
    name: 'Jane Smith',
    email: '<EMAIL>',
    department: 'Design',
    salary: 75000,
    status: <Chip label="Active" color="success" size="small" />,
  },
  {
    id: 3,
    avatar: <Avatar>MB</Avatar>,
    name: 'Mike Brown',
    email: '<EMAIL>',
    department: 'Marketing',
    salary: 65000,
    status: <Chip label="On Leave" color="warning" size="small" />,
  },
  {
    id: 4,
    avatar: <Avatar>SE</Avatar>,
    name: 'Sarah Evans',
    email: '<EMAIL>',
    department: 'Engineering',
    salary: 95000,
    status: <Chip label="Active" color="success" size="small" />,
  },
  {
    id: 5,
    avatar: <Avatar>DW</Avatar>,
    name: 'David Wilson',
    email: '<EMAIL>',
    department: 'Sales',
    salary: 70000,
    status: <Chip label="Inactive" color="error" size="small" />,
  },
];

export const Default: Story = {
  args: {
    columns: basicColumns,
    rows: basicRows.slice(0, 5),
  },
};

export const Sortable: Story = {
  args: {
    columns: basicColumns,
    rows: basicRows,
    sortable: true,
  },
};

export const Selectable: Story = {
  args: {
    columns: basicColumns,
    rows: basicRows.slice(0, 5),
    selectable: true,
    onRowSelect: (selectedIds) => console.log('Selected rows:', selectedIds),
  },
};

export const WithPagination: Story = {
  args: {
    columns: basicColumns,
    rows: basicRows,
    pagination: true,
    defaultRowsPerPage: 5,
    rowsPerPageOptions: [5, 10, 25],
  },
};

export const Dense: Story = {
  args: {
    columns: basicColumns,
    rows: basicRows.slice(0, 5),
    dense: true,
  },
};

export const FullFeatured: Story = {
  args: {
    columns: basicColumns,
    rows: basicRows,
    sortable: true,
    selectable: true,
    pagination: true,
    defaultRowsPerPage: 5,
    onRowSelect: (selectedIds) => console.log('Selected rows:', selectedIds),
    onRowClick: (row) => console.log('Clicked row:', row),
  },
};

export const EmployeeTable: Story = {
  args: {
    columns: employeeColumns,
    rows: employeeRows,
    sortable: true,
    selectable: true,
    onRowSelect: (selectedIds) => console.log('Selected employees:', selectedIds),
    onRowClick: (row) => console.log('Employee details:', row),
  },
};

export const CustomFormatting: Story = {
  args: {
    columns: [
      { id: 'name', label: 'Country', minWidth: 170 },
      { 
        id: 'population', 
        label: 'Population', 
        minWidth: 170, 
        align: 'right' as const,
        format: (value: number) => value.toLocaleString(),
      },
      { 
        id: 'size', 
        label: 'Size', 
        minWidth: 170, 
        align: 'right' as const,
        format: (value: number) => `${value.toLocaleString()} km²`,
      },
      {
        id: 'density',
        label: 'Density',
        minWidth: 170,
        align: 'right' as const,
        format: (value: number) => `${value.toFixed(2)} /km²`,
      },
    ],
    rows: basicRows.map(row => ({
      ...row,
      density: row.population / row.size,
    })),
    sortable: true,
    pagination: true,
    defaultRowsPerPage: 5,
  },
};

export const ClickableRows: Story = {
  args: {
    columns: employeeColumns,
    rows: employeeRows,
    onRowClick: (row) => alert(`Clicked on ${row.name}`),
  },
};
