import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Mu<PERSON>List } from './MuiList';
import { Paper, Avatar } from '@mui/material';
import {
  Inbox,
  Drafts,
  Send,
  ExpandLess,
  ExpandMore,
  StarBorder,
  Person,
  Work,
  Settings,
  Home,
  Folder,
  Description,
} from '@mui/icons-material';

const meta: Meta<typeof MuiList> = {
  title: 'MUI Components/Data Display/List',
  component: MuiList,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI List component for displaying structured data with various item types and nesting capabilities.',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof MuiList>;

const basicItems = [
  { id: 1, primary: 'Inbox', icon: <Inbox /> },
  { id: 2, primary: 'Starred', icon: <StarBorder /> },
  { id: 3, primary: 'Send email', icon: <Send /> },
  { id: 4, primary: 'Drafts', icon: <Drafts /> },
];

const contactItems = [
  {
    id: 1,
    primary: '<PERSON>',
    secondary: 'Software Engineer',
    avatarSrc: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
  },
  {
    id: 2,
    primary: 'Bob Smith',
    secondary: 'Product Manager',
    avatar: <Avatar>B</Avatar>,
  },
  {
    id: 3,
    primary: 'Charlie Brown',
    secondary: 'UX Designer',
    avatar: <Avatar>C</Avatar>,
  },
];

const nestedItems = [
  {
    id: 1,
    primary: 'Home',
    icon: <Home />,
    children: [
      { id: 11, primary: 'Living Room', icon: <Description /> },
      { id: 12, primary: 'Kitchen', icon: <Description /> },
      { id: 13, primary: 'Bedroom', icon: <Description /> },
    ],
  },
  {
    id: 2,
    primary: 'Work',
    icon: <Work />,
    children: [
      {
        id: 21,
        primary: 'Projects',
        icon: <Folder />,
        children: [
          { id: 211, primary: 'Project A', icon: <Description /> },
          { id: 212, primary: 'Project B', icon: <Description /> },
        ],
      },
      { id: 22, primary: 'Documents', icon: <Description /> },
    ],
  },
  {
    id: 3,
    primary: 'Settings',
    icon: <Settings />,
    children: [
      { id: 31, primary: 'Profile', icon: <Person /> },
      { id: 32, primary: 'Preferences', icon: <Settings /> },
    ],
  },
];

export const Default: Story = {
  render: () => (
    <Paper sx={{ width: 320 }}>
      <MuiList items={basicItems} />
    </Paper>
  ),
};

export const WithAvatars: Story = {
  render: () => (
    <Paper sx={{ width: 320 }}>
      <MuiList items={contactItems} />
    </Paper>
  ),
};

export const Clickable: Story = {
  render: () => (
    <Paper sx={{ width: 320 }}>
      <MuiList
        items={basicItems.map(item => ({
          ...item,
          onClick: () => alert(`Clicked: ${item.primary}`),
        }))}
        clickable
      />
    </Paper>
  ),
};

export const WithDividers: Story = {
  render: () => (
    <Paper sx={{ width: 320 }}>
      <MuiList items={contactItems} showDividers />
    </Paper>
  ),
};

export const Dense: Story = {
  render: () => (
    <Paper sx={{ width: 320 }}>
      <MuiList items={basicItems} dense />
    </Paper>
  ),
};

export const Nested: Story = {
  render: () => (
    <Paper sx={{ width: 320 }}>
      <MuiList items={nestedItems} collapsible />
    </Paper>
  ),
};

export const NestedClickable: Story = {
  render: () => (
    <Paper sx={{ width: 320 }}>
      <MuiList
        items={nestedItems.map(item => ({
          ...item,
          onClick: () => alert(`Clicked: ${item.primary}`),
          children: item.children?.map(child => ({
            ...child,
            onClick: () => alert(`Clicked: ${child.primary}`),
            children: child.children?.map(grandchild => ({
              ...grandchild,
              onClick: () => alert(`Clicked: ${grandchild.primary}`),
            })),
          })),
        }))}
        collapsible
        clickable
      />
    </Paper>
  ),
};

export const EmailList: Story = {
  render: () => {
    const emailItems = [
      {
        id: 1,
        primary: 'Brunch this weekend?',
        secondary: 'Ali Connors — I\'ll be in your neighborhood doing errands this weekend. Do you want to grab brunch?',
        avatar: <Avatar>A</Avatar>,
        divider: true,
      },
      {
        id: 2,
        primary: 'Summer BBQ',
        secondary: 'to Scott, Alex, Jennifer — Wish I could come, but I\'m out of town this weekend.',
        avatar: <Avatar>S</Avatar>,
        divider: true,
      },
      {
        id: 3,
        primary: 'Oui oui',
        secondary: 'Sandra Adams — Do you have Paris recommendations? Have you ever been?',
        avatar: <Avatar>S</Avatar>,
        divider: true,
      },
      {
        id: 4,
        primary: 'Birthday gift',
        secondary: 'Trevor Hansen — Have any ideas about what we should get Heidi for her birthday?',
        avatar: <Avatar>T</Avatar>,
      },
    ];

    return (
      <Paper sx={{ width: 400 }}>
        <MuiList items={emailItems} clickable />
      </Paper>
    );
  },
};

export const NavigationMenu: Story = {
  render: () => {
    const menuItems = [
      {
        id: 1,
        primary: 'Dashboard',
        icon: <Home />,
        onClick: () => console.log('Navigate to Dashboard'),
      },
      {
        id: 2,
        primary: 'Projects',
        icon: <Folder />,
        children: [
          {
            id: 21,
            primary: 'Active Projects',
            onClick: () => console.log('Navigate to Active Projects'),
          },
          {
            id: 22,
            primary: 'Completed Projects',
            onClick: () => console.log('Navigate to Completed Projects'),
          },
          {
            id: 23,
            primary: 'Archived Projects',
            onClick: () => console.log('Navigate to Archived Projects'),
          },
        ],
      },
      {
        id: 3,
        primary: 'Team',
        icon: <Person />,
        children: [
          {
            id: 31,
            primary: 'Members',
            onClick: () => console.log('Navigate to Team Members'),
          },
          {
            id: 32,
            primary: 'Roles',
            onClick: () => console.log('Navigate to Team Roles'),
          },
        ],
      },
      {
        id: 4,
        primary: 'Settings',
        icon: <Settings />,
        onClick: () => console.log('Navigate to Settings'),
      },
    ];

    return (
      <Paper sx={{ width: 280 }}>
        <MuiList items={menuItems} collapsible clickable />
      </Paper>
    );
  },
};
