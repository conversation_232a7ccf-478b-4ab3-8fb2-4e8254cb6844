import React from 'react';
import { Tooltip, TooltipProps } from '@mui/material';

export interface MuiTooltipProps extends TooltipProps {
  /**
   * The content of the tooltip
   */
  title: React.ReactNode;
  /**
   * The element that triggers the tooltip
   */
  children: React.ReactElement;
  /**
   * Placement of the tooltip
   */
  placement?: 
    | 'bottom-end' | 'bottom-start' | 'bottom'
    | 'left-end' | 'left-start' | 'left'
    | 'right-end' | 'right-start' | 'right'
    | 'top-end' | 'top-start' | 'top';
  /**
   * Whether the tooltip is open
   */
  open?: boolean;
  /**
   * Whether the tooltip should follow the cursor
   */
  followCursor?: boolean;
  /**
   * Delay in milliseconds before showing the tooltip
   */
  enterDelay?: number;
  /**
   * Delay in milliseconds before hiding the tooltip
   */
  leaveDelay?: number;
  /**
   * Whether the tooltip should be disabled
   */
  disabled?: boolean;
  /**
   * Whether the tooltip should have an arrow
   */
  arrow?: boolean;
  /**
   * Whether the tooltip is interactive
   */
  interactive?: boolean;
}

/**
 * A Material-UI Tooltip component for displaying helpful information on hover
 */
export const MuiTooltip: React.FC<MuiTooltipProps> = ({
  title,
  children,
  placement = 'bottom',
  open,
  followCursor = false,
  enterDelay = 100,
  leaveDelay = 0,
  disabled = false,
  arrow = false,
  interactive = false,
  ...props
}) => {
  if (disabled) {
    return children;
  }

  return (
    <Tooltip
      title={title}
      placement={placement}
      open={open}
      followCursor={followCursor}
      enterDelay={enterDelay}
      leaveDelay={leaveDelay}
      arrow={arrow}
      interactive={interactive}
      {...props}
    >
      {children}
    </Tooltip>
  );
};
