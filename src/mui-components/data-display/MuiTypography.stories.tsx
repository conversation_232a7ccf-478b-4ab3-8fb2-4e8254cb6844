import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MuiTypography } from './MuiTypography';
import { Stack, Box } from '@mui/material';

const meta: Meta<typeof MuiTypography> = {
  title: 'MUI Components/Data Display/Typography',
  component: MuiTypography,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Typography component for displaying text with consistent styling and theming.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'subtitle1', 'subtitle2', 'body1', 'body2', 'caption', 'button', 'overline'],
    },
    color: {
      control: { type: 'select' },
      options: ['inherit', 'primary', 'secondary', 'textPrimary', 'textSecondary', 'error', 'warning', 'info', 'success'],
    },
    align: {
      control: { type: 'select' },
      options: ['inherit', 'left', 'center', 'right', 'justify'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof MuiTypography>;

export const Default: Story = {
  args: {
    children: 'This is default typography text',
  },
};

export const Headings: Story = {
  render: () => (
    <Stack spacing={2} sx={{ width: 600 }}>
      <MuiTypography variant="h1">Heading 1</MuiTypography>
      <MuiTypography variant="h2">Heading 2</MuiTypography>
      <MuiTypography variant="h3">Heading 3</MuiTypography>
      <MuiTypography variant="h4">Heading 4</MuiTypography>
      <MuiTypography variant="h5">Heading 5</MuiTypography>
      <MuiTypography variant="h6">Heading 6</MuiTypography>
    </Stack>
  ),
};

export const Subtitles: Story = {
  render: () => (
    <Stack spacing={2} sx={{ width: 600 }}>
      <MuiTypography variant="subtitle1">
        Subtitle 1 - Lorem ipsum dolor sit amet, consectetur adipiscing elit
      </MuiTypography>
      <MuiTypography variant="subtitle2">
        Subtitle 2 - Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua
      </MuiTypography>
    </Stack>
  ),
};

export const Body: Story = {
  render: () => (
    <Stack spacing={2} sx={{ width: 600 }}>
      <MuiTypography variant="body1">
        Body 1 - Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.
      </MuiTypography>
      <MuiTypography variant="body2">
        Body 2 - Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.
      </MuiTypography>
    </Stack>
  ),
};

export const Colors: Story = {
  render: () => (
    <Stack spacing={1} sx={{ width: 600 }}>
      <MuiTypography color="primary">Primary color text</MuiTypography>
      <MuiTypography color="secondary">Secondary color text</MuiTypography>
      <MuiTypography color="textPrimary">Text primary color</MuiTypography>
      <MuiTypography color="textSecondary">Text secondary color</MuiTypography>
      <MuiTypography color="error">Error color text</MuiTypography>
      <MuiTypography color="warning">Warning color text</MuiTypography>
      <MuiTypography color="info">Info color text</MuiTypography>
      <MuiTypography color="success">Success color text</MuiTypography>
    </Stack>
  ),
};

export const Alignment: Story = {
  render: () => (
    <Stack spacing={2} sx={{ width: 600 }}>
      <MuiTypography align="left">Left aligned text</MuiTypography>
      <MuiTypography align="center">Center aligned text</MuiTypography>
      <MuiTypography align="right">Right aligned text</MuiTypography>
      <MuiTypography align="justify">
        Justified text - Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.
      </MuiTypography>
    </Stack>
  ),
};

export const TextStyles: Story = {
  render: () => (
    <Stack spacing={1} sx={{ width: 600 }}>
      <MuiTypography bold>Bold text</MuiTypography>
      <MuiTypography italic>Italic text</MuiTypography>
      <MuiTypography underline>Underlined text</MuiTypography>
      <MuiTypography bold italic underline>Bold, italic, and underlined text</MuiTypography>
    </Stack>
  ),
};

export const TextTransform: Story = {
  render: () => (
    <Stack spacing={1} sx={{ width: 600 }}>
      <MuiTypography uppercase>uppercase text</MuiTypography>
      <MuiTypography lowercase>LOWERCASE TEXT</MuiTypography>
      <MuiTypography capitalize>capitalize each word</MuiTypography>
    </Stack>
  ),
};

export const Truncation: Story = {
  render: () => (
    <Box sx={{ width: 200, border: '1px solid #ccc', p: 1 }}>
      <MuiTypography noWrap>
        This is a very long text that will be truncated with ellipsis when it overflows the container
      </MuiTypography>
    </Box>
  ),
};

export const Gutters: Story = {
  render: () => (
    <Box sx={{ width: 600, border: '1px solid #ccc', p: 2 }}>
      <MuiTypography variant="h4" gutterBottom>
        Heading with bottom gutter
      </MuiTypography>
      <MuiTypography variant="body1" gutterBottom>
        Paragraph with bottom gutter. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
      </MuiTypography>
      <MuiTypography variant="body1" gutterTop>
        Paragraph with top gutter. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
      </MuiTypography>
    </Box>
  ),
};

export const SpecialVariants: Story = {
  render: () => (
    <Stack spacing={2} sx={{ width: 600 }}>
      <MuiTypography variant="caption">Caption text - smaller and lighter</MuiTypography>
      <MuiTypography variant="overline">Overline text - all caps and spaced</MuiTypography>
      <MuiTypography variant="button">Button text style</MuiTypography>
    </Stack>
  ),
};

export const ArticleExample: Story = {
  render: () => (
    <Box sx={{ width: 600, p: 3 }}>
      <MuiTypography variant="h3" gutterBottom>
        The Future of Web Development
      </MuiTypography>
      
      <MuiTypography variant="subtitle1" color="textSecondary" gutterBottom>
        Exploring modern frameworks and technologies
      </MuiTypography>
      
      <MuiTypography variant="body1" gutterBottom>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
      </MuiTypography>
      
      <MuiTypography variant="h5" gutterBottom gutterTop>
        Key Technologies
      </MuiTypography>
      
      <MuiTypography variant="body1" gutterBottom>
        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
      </MuiTypography>
      
      <MuiTypography variant="caption" color="textSecondary">
        Published on March 15, 2024
      </MuiTypography>
    </Box>
  ),
};
