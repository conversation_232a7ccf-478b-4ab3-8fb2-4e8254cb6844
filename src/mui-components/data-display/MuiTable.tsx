import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableSortLabel,
  Paper,
  Checkbox,
  TablePagination,
  TableProps,
} from '@mui/material';

export interface TableColumn {
  id: string;
  label: string;
  minWidth?: number;
  align?: 'right' | 'left' | 'center';
  sortable?: boolean;
  format?: (value: any) => string;
}

export interface TableRowData {
  id: string | number;
  [key: string]: any;
}

export interface MuiTableProps extends TableProps {
  /**
   * Array of column definitions
   */
  columns: TableColumn[];
  /**
   * Array of row data
   */
  rows: TableRowData[];
  /**
   * Whether the table is sortable
   */
  sortable?: boolean;
  /**
   * Whether rows are selectable
   */
  selectable?: boolean;
  /**
   * Whether to show pagination
   */
  pagination?: boolean;
  /**
   * Rows per page options
   */
  rowsPerPageOptions?: number[];
  /**
   * Default rows per page
   */
  defaultRowsPerPage?: number;
  /**
   * Whether the table should be dense
   */
  dense?: boolean;
  /**
   * Callback when row is selected
   */
  onRowSelect?: (selectedIds: (string | number)[]) => void;
  /**
   * Callback when row is clicked
   */
  onRowClick?: (row: TableRowData) => void;
}

type Order = 'asc' | 'desc';

/**
 * A Material-UI Table component for displaying tabular data with sorting, selection, and pagination
 */
export const MuiTable: React.FC<MuiTableProps> = ({
  columns,
  rows,
  sortable = false,
  selectable = false,
  pagination = false,
  rowsPerPageOptions = [5, 10, 25],
  defaultRowsPerPage = 10,
  dense = false,
  onRowSelect,
  onRowClick,
  ...props
}) => {
  const [order, setOrder] = useState<Order>('asc');
  const [orderBy, setOrderBy] = useState<string>('');
  const [selected, setSelected] = useState<(string | number)[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(defaultRowsPerPage);

  const handleRequestSort = (property: string) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const handleSelectAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = rows.map((row) => row.id);
      setSelected(newSelected);
      onRowSelect?.(newSelected);
      return;
    }
    setSelected([]);
    onRowSelect?.([]);
  };

  const handleClick = (event: React.MouseEvent<unknown>, id: string | number, row: TableRowData) => {
    if (selectable) {
      const selectedIndex = selected.indexOf(id);
      let newSelected: (string | number)[] = [];

      if (selectedIndex === -1) {
        newSelected = newSelected.concat(selected, id);
      } else if (selectedIndex === 0) {
        newSelected = newSelected.concat(selected.slice(1));
      } else if (selectedIndex === selected.length - 1) {
        newSelected = newSelected.concat(selected.slice(0, -1));
      } else if (selectedIndex > 0) {
        newSelected = newSelected.concat(
          selected.slice(0, selectedIndex),
          selected.slice(selectedIndex + 1),
        );
      }

      setSelected(newSelected);
      onRowSelect?.(newSelected);
    }
    
    onRowClick?.(row);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const isSelected = (id: string | number) => selected.indexOf(id) !== -1;

  const sortedRows = React.useMemo(() => {
    if (!sortable || !orderBy) return rows;
    
    return [...rows].sort((a, b) => {
      const aValue = a[orderBy];
      const bValue = b[orderBy];
      
      if (bValue < aValue) {
        return order === 'desc' ? -1 : 1;
      }
      if (bValue > aValue) {
        return order === 'desc' ? 1 : -1;
      }
      return 0;
    });
  }, [rows, order, orderBy, sortable]);

  const paginatedRows = pagination 
    ? sortedRows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
    : sortedRows;

  const numSelected = selected.length;
  const rowCount = rows.length;

  return (
    <Paper>
      <TableContainer>
        <Table size={dense ? 'small' : 'medium'} {...props}>
          <TableHead>
            <TableRow>
              {selectable && (
                <TableCell padding="checkbox">
                  <Checkbox
                    color="primary"
                    indeterminate={numSelected > 0 && numSelected < rowCount}
                    checked={rowCount > 0 && numSelected === rowCount}
                    onChange={handleSelectAllClick}
                  />
                </TableCell>
              )}
              {columns.map((column) => (
                <TableCell
                  key={column.id}
                  align={column.align}
                  style={{ minWidth: column.minWidth }}
                  sortDirection={orderBy === column.id ? order : false}
                >
                  {sortable && column.sortable !== false ? (
                    <TableSortLabel
                      active={orderBy === column.id}
                      direction={orderBy === column.id ? order : 'asc'}
                      onClick={() => handleRequestSort(column.id)}
                    >
                      {column.label}
                    </TableSortLabel>
                  ) : (
                    column.label
                  )}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedRows.map((row) => {
              const isItemSelected = isSelected(row.id);
              
              return (
                <TableRow
                  hover
                  onClick={(event) => handleClick(event, row.id, row)}
                  role="checkbox"
                  aria-checked={isItemSelected}
                  tabIndex={-1}
                  key={row.id}
                  selected={isItemSelected}
                  sx={{ cursor: onRowClick || selectable ? 'pointer' : 'default' }}
                >
                  {selectable && (
                    <TableCell padding="checkbox">
                      <Checkbox
                        color="primary"
                        checked={isItemSelected}
                      />
                    </TableCell>
                  )}
                  {columns.map((column) => {
                    const value = row[column.id];
                    return (
                      <TableCell key={column.id} align={column.align}>
                        {column.format ? column.format(value) : value}
                      </TableCell>
                    );
                  })}
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
      {pagination && (
        <TablePagination
          rowsPerPageOptions={rowsPerPageOptions}
          component="div"
          count={rows.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      )}
    </Paper>
  );
};
