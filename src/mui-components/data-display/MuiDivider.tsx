import React from 'react';
import { Divider, DividerProps } from '@mui/material';

export interface MuiDividerProps extends DividerProps {
  /**
   * The orientation of the divider
   */
  orientation?: 'horizontal' | 'vertical';
  /**
   * The variant of the divider
   */
  variant?: 'fullWidth' | 'inset' | 'middle';
  /**
   * Text content to display in the divider
   */
  textAlign?: 'center' | 'left' | 'right';
  /**
   * Whether the divider should be flexible
   */
  flexItem?: boolean;
  /**
   * Whether the divider should be light
   */
  light?: boolean;
  /**
   * Custom content to display in the divider
   */
  children?: React.ReactNode;
}

/**
 * A Material-UI Divider component for separating content
 */
export const MuiDivider: React.FC<MuiDividerProps> = ({
  orientation = 'horizontal',
  variant = 'fullWidth',
  textAlign = 'center',
  flexItem = false,
  light = false,
  children,
  ...props
}) => {
  return (
    <Divider
      orientation={orientation}
      variant={variant}
      textAlign={textAlign}
      flexItem={flexItem}
      light={light}
      {...props}
    >
      {children}
    </Divider>
  );
};
