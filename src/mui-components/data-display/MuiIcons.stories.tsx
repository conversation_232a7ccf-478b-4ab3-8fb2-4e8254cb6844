import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { MuiIcon, IconName } from './MuiIcons';
import { 
  Grid, 
  Typography, 
  Box, 
  Stack, 
  IconButton, 
  Button,
  Card,
  CardContent,
} from '@mui/material';

const meta: Meta<typeof MuiIcon> = {
  title: 'MUI Components/Data Display/Icons',
  component: MuiIcon,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Icons component for displaying Material Design icons with consistent styling.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    name: {
      control: { type: 'select' },
      options: [
        'home', 'person', 'settings', 'search', 'favorite', 'star',
        'delete', 'edit', 'add', 'remove', 'check', 'close',
        'arrowBack', 'arrowForward', 'expandMore', 'expandLess',
        'menu', 'moreVert', 'notifications', 'email', 'phone',
        'locationOn', 'dateRange', 'accessTime', 'visibility', 'visibilityOff',
        'download', 'upload', 'share', 'print', 'save', 'refresh',
        'info', 'warning', 'error', 'checkCircle'
      ],
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large', 'inherit'],
    },
    color: {
      control: { type: 'select' },
      options: ['inherit', 'action', 'disabled', 'primary', 'secondary', 'error', 'info', 'success', 'warning'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof MuiIcon>;

export const Default: Story = {
  args: {
    name: 'home',
  },
};

export const Sizes: Story = {
  render: () => (
    <Stack direction="row" spacing={2} alignItems="center">
      <Box textAlign="center">
        <MuiIcon name="star" size="small" />
        <Typography variant="caption" display="block">Small</Typography>
      </Box>
      <Box textAlign="center">
        <MuiIcon name="star" size="medium" />
        <Typography variant="caption" display="block">Medium</Typography>
      </Box>
      <Box textAlign="center">
        <MuiIcon name="star" size="large" />
        <Typography variant="caption" display="block">Large</Typography>
      </Box>
    </Stack>
  ),
};

export const Colors: Story = {
  render: () => (
    <Stack spacing={2}>
      <Stack direction="row" spacing={2} alignItems="center">
        <MuiIcon name="favorite" color="primary" />
        <Typography>Primary</Typography>
      </Stack>
      <Stack direction="row" spacing={2} alignItems="center">
        <MuiIcon name="favorite" color="secondary" />
        <Typography>Secondary</Typography>
      </Stack>
      <Stack direction="row" spacing={2} alignItems="center">
        <MuiIcon name="favorite" color="error" />
        <Typography>Error</Typography>
      </Stack>
      <Stack direction="row" spacing={2} alignItems="center">
        <MuiIcon name="favorite" color="warning" />
        <Typography>Warning</Typography>
      </Stack>
      <Stack direction="row" spacing={2} alignItems="center">
        <MuiIcon name="favorite" color="info" />
        <Typography>Info</Typography>
      </Stack>
      <Stack direction="row" spacing={2} alignItems="center">
        <MuiIcon name="favorite" color="success" />
        <Typography>Success</Typography>
      </Stack>
    </Stack>
  ),
};

export const AllIcons: Story = {
  render: () => {
    const iconNames: IconName[] = [
      'home', 'person', 'settings', 'search', 'favorite', 'star',
      'delete', 'edit', 'add', 'remove', 'check', 'close',
      'arrowBack', 'arrowForward', 'expandMore', 'expandLess',
      'menu', 'moreVert', 'notifications', 'email', 'phone',
      'locationOn', 'dateRange', 'accessTime', 'visibility', 'visibilityOff',
      'download', 'upload', 'share', 'print', 'save', 'refresh',
      'info', 'warning', 'error', 'checkCircle'
    ];

    return (
      <Grid container spacing={2} sx={{ maxWidth: 800 }}>
        {iconNames.map((iconName) => (
          <Grid item xs={3} sm={2} md={1.5} key={iconName}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                p: 1,
                border: '1px solid #e0e0e0',
                borderRadius: 1,
                '&:hover': {
                  backgroundColor: 'action.hover',
                },
              }}
            >
              <MuiIcon name={iconName} />
              <Typography variant="caption" sx={{ mt: 0.5, fontSize: '0.7rem' }}>
                {iconName}
              </Typography>
            </Box>
          </Grid>
        ))}
      </Grid>
    );
  },
};

export const InButtons: Story = {
  render: () => (
    <Stack direction="row" spacing={2}>
      <Button variant="contained" startIcon={<MuiIcon name="add" />}>
        Add Item
      </Button>
      <Button variant="outlined" startIcon={<MuiIcon name="edit" />}>
        Edit
      </Button>
      <Button variant="text" startIcon={<MuiIcon name="delete" />} color="error">
        Delete
      </Button>
      <IconButton color="primary">
        <MuiIcon name="favorite" />
      </IconButton>
      <IconButton color="secondary">
        <MuiIcon name="share" />
      </IconButton>
    </Stack>
  ),
};

export const StatusIcons: Story = {
  render: () => (
    <Stack spacing={2}>
      <Stack direction="row" spacing={2} alignItems="center">
        <MuiIcon name="checkCircle" color="success" />
        <Typography>Success status</Typography>
      </Stack>
      <Stack direction="row" spacing={2} alignItems="center">
        <MuiIcon name="error" color="error" />
        <Typography>Error status</Typography>
      </Stack>
      <Stack direction="row" spacing={2} alignItems="center">
        <MuiIcon name="warning" color="warning" />
        <Typography>Warning status</Typography>
      </Stack>
      <Stack direction="row" spacing={2} alignItems="center">
        <MuiIcon name="info" color="info" />
        <Typography>Info status</Typography>
      </Stack>
    </Stack>
  ),
};

export const NavigationIcons: Story = {
  render: () => (
    <Stack direction="row" spacing={1}>
      <IconButton>
        <MuiIcon name="menu" />
      </IconButton>
      <IconButton>
        <MuiIcon name="arrowBack" />
      </IconButton>
      <IconButton>
        <MuiIcon name="home" />
      </IconButton>
      <IconButton>
        <MuiIcon name="arrowForward" />
      </IconButton>
      <IconButton>
        <MuiIcon name="moreVert" />
      </IconButton>
    </Stack>
  ),
};

export const ContactCard: Story = {
  render: () => (
    <Card sx={{ maxWidth: 300 }}>
      <CardContent>
        <Stack spacing={2}>
          <Stack direction="row" spacing={2} alignItems="center">
            <MuiIcon name="person" color="primary" />
            <Typography variant="h6">John Doe</Typography>
          </Stack>
          <Stack direction="row" spacing={2} alignItems="center">
            <MuiIcon name="email" color="action" />
            <Typography><EMAIL></Typography>
          </Stack>
          <Stack direction="row" spacing={2} alignItems="center">
            <MuiIcon name="phone" color="action" />
            <Typography>+****************</Typography>
          </Stack>
          <Stack direction="row" spacing={2} alignItems="center">
            <MuiIcon name="locationOn" color="action" />
            <Typography>New York, NY</Typography>
          </Stack>
        </Stack>
      </CardContent>
    </Card>
  ),
};
