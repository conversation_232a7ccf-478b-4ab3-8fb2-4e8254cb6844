import React from 'react';
import { Avatar, AvatarProps } from '@mui/material';

export interface MuiAvatarProps extends AvatarProps {
  /**
   * The name to generate initials from
   */
  name?: string;
  /**
   * The source URL for the avatar image
   */
  src?: string;
  /**
   * Alternative text for the avatar image
   */
  alt?: string;
  /**
   * The size variant of the avatar
   */
  size?: 'small' | 'medium' | 'large' | number;
}

/**
 * A Material-UI Avatar component with automatic initial generation
 */
export const MuiAvatar: React.FC<MuiAvatarProps> = ({
  name,
  src,
  alt,
  size = 'medium',
  children,
  ...props
}) => {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getSizeStyles = (size: 'small' | 'medium' | 'large' | number) => {
    if (typeof size === 'number') {
      return { width: size, height: size };
    }
    
    switch (size) {
      case 'small':
        return { width: 32, height: 32 };
      case 'large':
        return { width: 56, height: 56 };
      default:
        return { width: 40, height: 40 };
    }
  };

  return (
    <Avatar
      src={src}
      alt={alt || name}
      sx={{ ...getSizeStyles(size), ...props.sx }}
      {...props}
    >
      {children || (name && !src ? getInitials(name) : undefined)}
    </Avatar>
  );
};
