import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { MuiDivider } from './MuiDivider';
import { 
  Stack, 
  Typography, 
  Box, 
  List, 
  ListItem, 
  ListItemText,
  Chip,
} from '@mui/material';

const meta: Meta<typeof MuiDivider> = {
  title: 'MUI Components/Data Display/Divider',
  component: MuiDivider,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Divider component for separating content sections.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    orientation: {
      control: { type: 'select' },
      options: ['horizontal', 'vertical'],
    },
    variant: {
      control: { type: 'select' },
      options: ['fullWidth', 'inset', 'middle'],
    },
    textAlign: {
      control: { type: 'select' },
      options: ['center', 'left', 'right'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof MuiDivider>;

export const Default: Story = {
  render: () => (
    <Box sx={{ width: 400 }}>
      <Typography variant="body1">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit.
      </Typography>
      <MuiDivider />
      <Typography variant="body1">
        Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
      </Typography>
    </Box>
  ),
};

export const WithText: Story = {
  render: () => (
    <Box sx={{ width: 400 }}>
      <Typography variant="body1">
        Content above the divider
      </Typography>
      <MuiDivider>OR</MuiDivider>
      <Typography variant="body1">
        Content below the divider
      </Typography>
    </Box>
  ),
};

export const TextAlignment: Story = {
  render: () => (
    <Stack spacing={2} sx={{ width: 400 }}>
      <Typography variant="body1">Content above</Typography>
      <MuiDivider textAlign="left">Left</MuiDivider>
      <Typography variant="body1">Content middle</Typography>
      <MuiDivider textAlign="center">Center</MuiDivider>
      <Typography variant="body1">Content middle</Typography>
      <MuiDivider textAlign="right">Right</MuiDivider>
      <Typography variant="body1">Content below</Typography>
    </Stack>
  ),
};

export const Variants: Story = {
  render: () => (
    <Stack spacing={2} sx={{ width: 400 }}>
      <Typography variant="h6">Full Width</Typography>
      <MuiDivider variant="fullWidth" />
      
      <Typography variant="h6">Inset</Typography>
      <MuiDivider variant="inset" />
      
      <Typography variant="h6">Middle</Typography>
      <MuiDivider variant="middle" />
    </Stack>
  ),
};

export const Vertical: Story = {
  render: () => (
    <Box sx={{ display: 'flex', alignItems: 'center', height: 100 }}>
      <Typography variant="body1">Left Content</Typography>
      <MuiDivider orientation="vertical" flexItem sx={{ mx: 2 }} />
      <Typography variant="body1">Right Content</Typography>
    </Box>
  ),
};

export const InList: Story = {
  render: () => (
    <List sx={{ width: 300, bgcolor: 'background.paper' }}>
      <ListItem>
        <ListItemText primary="Item 1" secondary="Description for item 1" />
      </ListItem>
      <MuiDivider variant="inset" />
      <ListItem>
        <ListItemText primary="Item 2" secondary="Description for item 2" />
      </ListItem>
      <MuiDivider variant="inset" />
      <ListItem>
        <ListItemText primary="Item 3" secondary="Description for item 3" />
      </ListItem>
    </List>
  ),
};

export const WithChips: Story = {
  render: () => (
    <Box sx={{ width: 400 }}>
      <Typography variant="h6" gutterBottom>
        Skills
      </Typography>
      <Stack direction="row" spacing={1} flexWrap="wrap">
        <Chip label="React" size="small" />
        <Chip label="TypeScript" size="small" />
        <Chip label="JavaScript" size="small" />
      </Stack>
      
      <MuiDivider sx={{ my: 2 }}>Experience</MuiDivider>
      
      <Stack direction="row" spacing={1} flexWrap="wrap">
        <Chip label="5+ years" size="small" color="primary" />
        <Chip label="Senior Level" size="small" color="success" />
      </Stack>
    </Box>
  ),
};

export const Light: Story = {
  render: () => (
    <Box sx={{ width: 400, bgcolor: 'grey.900', p: 2, color: 'white' }}>
      <Typography variant="body1" color="inherit">
        Dark background content
      </Typography>
      <MuiDivider light sx={{ my: 2 }} />
      <Typography variant="body1" color="inherit">
        More dark background content
      </Typography>
    </Box>
  ),
};

export const FlexItem: Story = {
  render: () => (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
      <Typography variant="body1">Start</Typography>
      <MuiDivider orientation="vertical" flexItem />
      <Typography variant="body1">Middle</Typography>
      <MuiDivider orientation="vertical" flexItem />
      <Typography variant="body1">End</Typography>
    </Box>
  ),
};
