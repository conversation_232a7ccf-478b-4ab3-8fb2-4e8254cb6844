import React from 'react';
import { Chip, ChipProps, Avatar } from '@mui/material';

export interface MuiChipProps extends ChipProps {
  /**
   * The label text of the chip
   */
  label: string;
  /**
   * The variant of the chip
   */
  variant?: 'filled' | 'outlined';
  /**
   * The color of the chip
   */
  color?: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  /**
   * The size of the chip
   */
  size?: 'small' | 'medium';
  /**
   * Icon element to display at the beginning
   */
  icon?: React.ReactElement;
  /**
   * Avatar element to display at the beginning
   */
  avatar?: React.ReactElement;
  /**
   * Avatar image source
   */
  avatarSrc?: string;
  /**
   * Avatar alt text
   */
  avatarAlt?: string;
  /**
   * Whether the chip can be deleted
   */
  deletable?: boolean;
  /**
   * Callback when delete is clicked
   */
  onDelete?: () => void;
  /**
   * Whether the chip is clickable
   */
  clickable?: boolean;
  /**
   * Callback when chip is clicked
   */
  onClick?: () => void;
}

/**
 * A Material-UI Chip component for displaying compact information
 */
export const MuiChip: React.FC<MuiChipProps> = ({
  label,
  variant = 'filled',
  color = 'default',
  size = 'medium',
  icon,
  avatar,
  avatarSrc,
  avatarAlt,
  deletable = false,
  onDelete,
  clickable = false,
  onClick,
  ...props
}) => {
  const getAvatar = () => {
    if (avatar) return avatar;
    if (avatarSrc) {
      return <Avatar src={avatarSrc} alt={avatarAlt || label} />;
    }
    return undefined;
  };

  return (
    <Chip
      label={label}
      variant={variant}
      color={color}
      size={size}
      icon={icon}
      avatar={getAvatar()}
      onDelete={deletable ? onDelete : undefined}
      clickable={clickable}
      onClick={clickable ? onClick : undefined}
      {...props}
    />
  );
};
