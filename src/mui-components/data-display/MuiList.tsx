import React from 'react';
import {
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  ListProps,
  Collapse,
} from '@mui/material';
import { ExpandLess, ExpandMore } from '@mui/icons-material';

export interface ListItemData {
  id: string | number;
  primary: string;
  secondary?: string;
  icon?: React.ReactElement;
  avatar?: React.ReactElement;
  avatarSrc?: string;
  avatarAlt?: string;
  disabled?: boolean;
  divider?: boolean;
  onClick?: () => void;
  children?: ListItemData[];
}

export interface MuiListProps extends ListProps {
  /**
   * Array of list items
   */
  items: ListItemData[];
  /**
   * Whether the list is dense
   */
  dense?: boolean;
  /**
   * Whether list items are clickable
   */
  clickable?: boolean;
  /**
   * Whether to show dividers between items
   */
  showDividers?: boolean;
  /**
   * Whether nested items are collapsible
   */
  collapsible?: boolean;
  /**
   * Maximum nesting level
   */
  maxNestLevel?: number;
}

interface ListItemComponentProps {
  item: ListItemData;
  clickable: boolean;
  showDividers: boolean;
  collapsible: boolean;
  nestLevel: number;
  maxNestLevel: number;
}

const ListItemComponent: React.FC<ListItemComponentProps> = ({
  item,
  clickable,
  showDividers,
  collapsible,
  nestLevel,
  maxNestLevel,
}) => {
  const [open, setOpen] = React.useState(false);
  const hasChildren = item.children && item.children.length > 0;
  const canNest = nestLevel < maxNestLevel;

  const handleClick = () => {
    if (hasChildren && collapsible && canNest) {
      setOpen(!open);
    } else if (item.onClick) {
      item.onClick();
    }
  };

  const getAvatar = () => {
    if (item.avatar) return item.avatar;
    if (item.avatarSrc) {
      return <Avatar src={item.avatarSrc} alt={item.avatarAlt || item.primary} />;
    }
    return undefined;
  };

  const ItemContent = clickable || (hasChildren && collapsible && canNest) ? ListItemButton : ListItem;

  return (
    <>
      <ItemContent
        onClick={handleClick}
        disabled={item.disabled}
        sx={{ pl: nestLevel * 2 }}
      >
        {item.icon && <ListItemIcon>{item.icon}</ListItemIcon>}
        {getAvatar() && <ListItemAvatar>{getAvatar()}</ListItemAvatar>}
        <ListItemText
          primary={item.primary}
          secondary={item.secondary}
        />
        {hasChildren && collapsible && canNest && (
          open ? <ExpandLess /> : <ExpandMore />
        )}
      </ItemContent>
      
      {hasChildren && collapsible && canNest && (
        <Collapse in={open} timeout="auto" unmountOnExit>
          <List component="div" disablePadding>
            {item.children!.map((child) => (
              <ListItemComponent
                key={child.id}
                item={child}
                clickable={clickable}
                showDividers={showDividers}
                collapsible={collapsible}
                nestLevel={nestLevel + 1}
                maxNestLevel={maxNestLevel}
              />
            ))}
          </List>
        </Collapse>
      )}
      
      {(item.divider || showDividers) && <Divider />}
    </>
  );
};

/**
 * A Material-UI List component for displaying structured data
 */
export const MuiList: React.FC<MuiListProps> = ({
  items,
  dense = false,
  clickable = false,
  showDividers = false,
  collapsible = false,
  maxNestLevel = 3,
  ...props
}) => {
  return (
    <List dense={dense} {...props}>
      {items.map((item) => (
        <ListItemComponent
          key={item.id}
          item={item}
          clickable={clickable}
          showDividers={showDividers}
          collapsible={collapsible}
          nestLevel={0}
          maxNestLevel={maxNestLevel}
        />
      ))}
    </List>
  );
};
