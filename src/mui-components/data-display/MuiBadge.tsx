import React from 'react';
import { Badge, BadgeProps } from '@mui/material';

export interface MuiBadgeProps extends BadgeProps {
  /**
   * The content to be wrapped by the badge
   */
  children: React.ReactNode;
  /**
   * The content rendered within the badge
   */
  badgeContent?: React.ReactNode;
  /**
   * The color of the badge
   */
  color?: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  /**
   * The variant of the badge
   */
  variant?: 'standard' | 'dot';
  /**
   * The anchor origin of the badge
   */
  anchorOrigin?: {
    vertical: 'top' | 'bottom';
    horizontal: 'left' | 'right';
  };
  /**
   * Max count to show
   */
  max?: number;
  /**
   * Whether to show zero
   */
  showZero?: boolean;
}

/**
 * A Material-UI Badge component for displaying notifications and status
 */
export const MuiBadge: React.FC<MuiBadgeProps> = ({
  children,
  badgeContent,
  color = 'primary',
  variant = 'standard',
  anchorOrigin = {
    vertical: 'top',
    horizontal: 'right',
  },
  max = 99,
  showZero = false,
  ...props
}) => {
  return (
    <Badge
      badgeContent={badgeContent}
      color={color}
      variant={variant}
      anchorOrigin={anchorOrigin}
      max={max}
      showZero={showZero}
      {...props}
    >
      {children}
    </Badge>
  );
};
