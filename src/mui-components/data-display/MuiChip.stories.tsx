import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MuiChip } from './MuiChip';
import { useState } from 'react';
import { Stack, Avatar } from '@mui/material';
import FaceIcon from '@mui/icons-material/Face';
import DoneIcon from '@mui/icons-material/Done';
import TagIcon from '@mui/icons-material/Tag';

const meta: Meta<typeof MuiChip> = {
  title: 'MUI Components/Data Display/Chip',
  component: MuiChip,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Chip component for displaying compact information like tags, categories, or user selections.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['filled', 'outlined'],
    },
    color: {
      control: { type: 'select' },
      options: ['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning'],
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof MuiChip>;

export const Default: Story = {
  args: {
    label: 'Default Chip',
  },
};

export const WithIcon: Story = {
  args: {
    label: 'Chip with Icon',
    icon: <TagIcon />,
  },
};

export const WithAvatar: Story = {
  args: {
    label: 'John Doe',
    avatarSrc: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    avatarAlt: 'John Doe',
  },
};

export const Deletable: Story = {
  render: (args) => {
    const [chips, setChips] = useState([
      { id: 1, label: 'React' },
      { id: 2, label: 'TypeScript' },
      { id: 3, label: 'Material-UI' },
    ]);

    const handleDelete = (chipId: number) => {
      setChips(chips.filter(chip => chip.id !== chipId));
    };

    return (
      <Stack direction="row" spacing={1}>
        {chips.map((chip) => (
          <MuiChip
            key={chip.id}
            label={chip.label}
            deletable
            onDelete={() => handleDelete(chip.id)}
          />
        ))}
      </Stack>
    );
  },
  args: {},
};

export const Clickable: Story = {
  args: {
    label: 'Clickable Chip',
    clickable: true,
    onClick: () => alert('Chip clicked!'),
  },
};

export const Colors: Story = {
  render: () => (
    <Stack direction="row" spacing={1} flexWrap="wrap">
      <MuiChip label="Default" color="default" />
      <MuiChip label="Primary" color="primary" />
      <MuiChip label="Secondary" color="secondary" />
      <MuiChip label="Success" color="success" />
      <MuiChip label="Error" color="error" />
      <MuiChip label="Warning" color="warning" />
      <MuiChip label="Info" color="info" />
    </Stack>
  ),
};

export const Variants: Story = {
  render: () => (
    <Stack spacing={2}>
      <Stack direction="row" spacing={1}>
        <MuiChip label="Filled Primary" variant="filled" color="primary" />
        <MuiChip label="Filled Secondary" variant="filled" color="secondary" />
        <MuiChip label="Filled Success" variant="filled" color="success" />
      </Stack>
      <Stack direction="row" spacing={1}>
        <MuiChip label="Outlined Primary" variant="outlined" color="primary" />
        <MuiChip label="Outlined Secondary" variant="outlined" color="secondary" />
        <MuiChip label="Outlined Success" variant="outlined" color="success" />
      </Stack>
    </Stack>
  ),
};

export const Sizes: Story = {
  render: () => (
    <Stack direction="row" spacing={1} alignItems="center">
      <MuiChip label="Small" size="small" />
      <MuiChip label="Medium" size="medium" />
    </Stack>
  ),
};

export const TagExample: Story = {
  render: () => {
    const [selectedTags, setSelectedTags] = useState<string[]>(['React', 'TypeScript']);
    const availableTags = ['React', 'TypeScript', 'JavaScript', 'Material-UI', 'Node.js', 'Python'];

    const handleTagClick = (tag: string) => {
      if (selectedTags.includes(tag)) {
        setSelectedTags(selectedTags.filter(t => t !== tag));
      } else {
        setSelectedTags([...selectedTags, tag]);
      }
    };

    return (
      <Stack spacing={2}>
        <div>Available Tags:</div>
        <Stack direction="row" spacing={1} flexWrap="wrap">
          {availableTags.map((tag) => (
            <MuiChip
              key={tag}
              label={tag}
              clickable
              color={selectedTags.includes(tag) ? 'primary' : 'default'}
              variant={selectedTags.includes(tag) ? 'filled' : 'outlined'}
              icon={selectedTags.includes(tag) ? <DoneIcon /> : undefined}
              onClick={() => handleTagClick(tag)}
            />
          ))}
        </Stack>
      </Stack>
    );
  },
};

export const UserChips: Story = {
  render: () => (
    <Stack direction="row" spacing={1} flexWrap="wrap">
      <MuiChip
        label="Alice Johnson"
        avatar={<Avatar>A</Avatar>}
        deletable
        onDelete={() => console.log('Delete Alice')}
      />
      <MuiChip
        label="Bob Smith"
        avatar={<Avatar>B</Avatar>}
        deletable
        onDelete={() => console.log('Delete Bob')}
      />
      <MuiChip
        label="Charlie Brown"
        avatar={<Avatar>C</Avatar>}
        deletable
        onDelete={() => console.log('Delete Charlie')}
      />
    </Stack>
  ),
};
