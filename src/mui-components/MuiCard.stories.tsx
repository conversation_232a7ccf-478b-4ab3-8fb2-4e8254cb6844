import type { Meta, StoryObj } from '@storybook/react';
import { MuiCard } from './MuiCard';

const meta: Meta<typeof MuiCard> = {
  title: 'MUI Components/Surface/Card',
  component: MuiCard,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Card component with theme support and customizable actions.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    title: {
      control: { type: 'text' },
    },
    description: {
      control: { type: 'text' },
    },
    imageUrl: {
      control: { type: 'text' },
    },
    imageAlt: {
      control: { type: 'text' },
    },
    primaryAction: {
      control: { type: 'text' },
    },
    secondaryAction: {
      control: { type: 'text' },
    },
    elevation: {
      control: { type: 'range', min: 0, max: 24, step: 1 },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    title: 'Sample Card',
    description: 'This is a sample card component built with Material-UI. It demonstrates how MUI components work within Storybook with proper theming.',
    primaryAction: 'Learn More',
    secondaryAction: 'Share',
  },
};

export const WithImage: Story = {
  args: {
    title: 'Beautiful Landscape',
    description: 'A stunning landscape photograph that showcases the beauty of nature.',
    imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    imageAlt: 'Beautiful mountain landscape',
    primaryAction: 'View Full Size',
    secondaryAction: 'Download',
  },
};

export const NoActions: Story = {
  args: {
    title: 'Information Card',
    description: 'This card displays information without any action buttons.',
  },
};

export const SingleAction: Story = {
  args: {
    title: 'Product Card',
    description: 'A product card with a single primary action.',
    primaryAction: 'Buy Now',
  },
};

export const Elevated: Story = {
  args: {
    title: 'Elevated Card',
    description: 'This card has a higher elevation for more prominence.',
    elevation: 8,
    primaryAction: 'Get Started',
  },
};
