import React from 'react';
import { Skeleton, SkeletonProps } from '@mui/material';

export interface MuiSkeletonProps extends SkeletonProps {
  /**
   * The type of content being loaded
   */
  variant?: 'text' | 'rectangular' | 'rounded' | 'circular';
  /**
   * The animation type
   */
  animation?: 'pulse' | 'wave' | false;
  /**
   * Width of the skeleton
   */
  width?: number | string;
  /**
   * Height of the skeleton
   */
  height?: number | string;
  /**
   * Number of lines for text skeleton
   */
  lines?: number;
}

/**
 * A Material-UI Skeleton component for showing loading placeholders
 */
export const MuiSkeleton: React.FC<MuiSkeletonProps> = ({
  variant = 'text',
  animation = 'pulse',
  width,
  height,
  lines = 1,
  ...props
}) => {
  if (variant === 'text' && lines > 1) {
    return (
      <>
        {Array.from({ length: lines }, (_, index) => (
          <Skeleton
            key={index}
            variant="text"
            animation={animation}
            width={index === lines - 1 ? '60%' : width}
            height={height}
            {...props}
          />
        ))}
      </>
    );
  }

  return (
    <Skeleton
      variant={variant}
      animation={animation}
      width={width}
      height={height}
      {...props}
    />
  );
};
