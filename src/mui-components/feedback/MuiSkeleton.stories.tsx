import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { MuiSkeleton } from './MuiSkeleton';
import { useState } from 'react';
import { 
  Stack, 
  Card, 
  CardContent, 
  CardMedia, 
  Typography, 
  Button, 
  Box,
  Avatar,
} from '@mui/material';

const meta: Meta<typeof MuiSkeleton> = {
  title: 'MUI Components/Feedback/Skeleton',
  component: MuiSkeleton,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Skeleton component for showing loading placeholders while content is being fetched.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['text', 'rectangular', 'rounded', 'circular'],
    },
    animation: {
      control: { type: 'select' },
      options: ['pulse', 'wave', false],
    },
  },
};

export default meta;
type Story = StoryObj<typeof MuiSkeleton>;

export const Text: Story = {
  args: {
    variant: 'text',
    width: 200,
  },
};

export const MultipleLines: Story = {
  args: {
    variant: 'text',
    lines: 3,
    width: 300,
  },
};

export const Rectangular: Story = {
  args: {
    variant: 'rectangular',
    width: 200,
    height: 100,
  },
};

export const Rounded: Story = {
  args: {
    variant: 'rounded',
    width: 200,
    height: 100,
  },
};

export const Circular: Story = {
  args: {
    variant: 'circular',
    width: 60,
    height: 60,
  },
};

export const Animations: Story = {
  render: () => (
    <Stack spacing={2} sx={{ width: 300 }}>
      <Typography variant="h6">Pulse Animation</Typography>
      <MuiSkeleton variant="text" animation="pulse" />
      
      <Typography variant="h6">Wave Animation</Typography>
      <MuiSkeleton variant="text" animation="wave" />
      
      <Typography variant="h6">No Animation</Typography>
      <MuiSkeleton variant="text" animation={false} />
    </Stack>
  ),
};

export const CardSkeleton: Story = {
  render: () => {
    const [loading, setLoading] = useState(true);

    return (
      <Box sx={{ width: 300 }}>
        <Button 
          variant="outlined" 
          onClick={() => setLoading(!loading)}
          sx={{ mb: 2 }}
        >
          {loading ? 'Show Content' : 'Show Skeleton'}
        </Button>
        
        <Card>
          {loading ? (
            <>
              <MuiSkeleton variant="rectangular" height={140} />
              <CardContent>
                <MuiSkeleton variant="text" height={32} width="80%" />
                <MuiSkeleton variant="text" lines={2} />
              </CardContent>
            </>
          ) : (
            <>
              <CardMedia
                component="img"
                height="140"
                image="https://images.unsplash.com/photo-1551963831-b3b1ca40c98e?w=300"
                alt="Breakfast"
              />
              <CardContent>
                <Typography gutterBottom variant="h5" component="div">
                  Delicious Breakfast
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  A wonderful breakfast spread with fresh ingredients and amazing flavors that will start your day right.
                </Typography>
              </CardContent>
            </>
          )}
        </Card>
      </Box>
    );
  },
};

export const ProfileSkeleton: Story = {
  render: () => {
    const [loading, setLoading] = useState(true);

    return (
      <Box sx={{ width: 400 }}>
        <Button 
          variant="outlined" 
          onClick={() => setLoading(!loading)}
          sx={{ mb: 2 }}
        >
          {loading ? 'Show Profile' : 'Show Skeleton'}
        </Button>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {loading ? (
            <MuiSkeleton variant="circular" width={60} height={60} />
          ) : (
            <Avatar sx={{ width: 60, height: 60 }}>JD</Avatar>
          )}
          
          <Box sx={{ flex: 1 }}>
            {loading ? (
              <>
                <MuiSkeleton variant="text" width="40%" height={24} />
                <MuiSkeleton variant="text" width="60%" height={20} />
                <MuiSkeleton variant="text" width="80%" height={16} />
              </>
            ) : (
              <>
                <Typography variant="h6">John Doe</Typography>
                <Typography variant="body2" color="text.secondary">
                  Software Engineer
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <EMAIL>
                </Typography>
              </>
            )}
          </Box>
        </Box>
      </Box>
    );
  },
};

export const ListSkeleton: Story = {
  render: () => (
    <Stack spacing={1} sx={{ width: 350 }}>
      {Array.from({ length: 5 }, (_, index) => (
        <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <MuiSkeleton variant="circular" width={40} height={40} />
          <Box sx={{ flex: 1 }}>
            <MuiSkeleton variant="text" width="60%" />
            <MuiSkeleton variant="text" width="40%" />
          </Box>
        </Box>
      ))}
    </Stack>
  ),
};
