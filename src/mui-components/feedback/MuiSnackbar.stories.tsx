import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { MuiSnackbar } from './MuiSnackbar';
import { useState } from 'react';
import { But<PERSON>, Stack, IconButton } from '@mui/material';
import UndoIcon from '@mui/icons-material/Undo';

const meta: Meta<typeof MuiSnackbar> = {
  title: 'MUI Components/Feedback/Snackbar',
  component: MuiSnackbar,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Snackbar component for showing brief messages and notifications.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    severity: {
      control: { type: 'select' },
      options: ['error', 'warning', 'info', 'success'],
    },
    alertVariant: {
      control: { type: 'select' },
      options: ['filled', 'outlined', 'standard'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof MuiSnackbar>;

export const Default: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false);
    
    return (
      <>
        <Button variant="outlined" onClick={() => setOpen(true)}>
          Show Snackbar
        </Button>
        <MuiSnackbar
          {...args}
          open={open}
          onClose={() => setOpen(false)}
        />
      </>
    );
  },
  args: {
    message: 'This is a simple snackbar message',
  },
};

export const WithAlert: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false);
    
    return (
      <>
        <Button variant="outlined" onClick={() => setOpen(true)}>
          Show Success Alert
        </Button>
        <MuiSnackbar
          {...args}
          open={open}
          onClose={() => setOpen(false)}
        />
      </>
    );
  },
  args: {
    message: 'Operation completed successfully!',
    useAlert: true,
    severity: 'success',
  },
};

export const Severities: Story = {
  render: () => {
    const [openStates, setOpenStates] = useState({
      success: false,
      info: false,
      warning: false,
      error: false,
    });

    const handleOpen = (severity: keyof typeof openStates) => {
      setOpenStates(prev => ({ ...prev, [severity]: true }));
    };

    const handleClose = (severity: keyof typeof openStates) => {
      setOpenStates(prev => ({ ...prev, [severity]: false }));
    };

    return (
      <>
        <Stack direction="row" spacing={2}>
          <Button 
            variant="outlined" 
            color="success"
            onClick={() => handleOpen('success')}
          >
            Success
          </Button>
          <Button 
            variant="outlined" 
            color="info"
            onClick={() => handleOpen('info')}
          >
            Info
          </Button>
          <Button 
            variant="outlined" 
            color="warning"
            onClick={() => handleOpen('warning')}
          >
            Warning
          </Button>
          <Button 
            variant="outlined" 
            color="error"
            onClick={() => handleOpen('error')}
          >
            Error
          </Button>
        </Stack>

        <MuiSnackbar
          open={openStates.success}
          onClose={() => handleClose('success')}
          message="This is a success message!"
          useAlert
          severity="success"
        />
        <MuiSnackbar
          open={openStates.info}
          onClose={() => handleClose('info')}
          message="This is an info message!"
          useAlert
          severity="info"
        />
        <MuiSnackbar
          open={openStates.warning}
          onClose={() => handleClose('warning')}
          message="This is a warning message!"
          useAlert
          severity="warning"
        />
        <MuiSnackbar
          open={openStates.error}
          onClose={() => handleClose('error')}
          message="This is an error message!"
          useAlert
          severity="error"
        />
      </>
    );
  },
};

export const WithCustomAction: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false);
    
    const undoAction = (
      <IconButton
        size="small"
        aria-label="undo"
        color="inherit"
        onClick={() => {
          console.log('Undo action triggered');
          setOpen(false);
        }}
      >
        <UndoIcon fontSize="small" />
      </IconButton>
    );
    
    return (
      <>
        <Button variant="outlined" onClick={() => setOpen(true)}>
          Show with Undo Action
        </Button>
        <MuiSnackbar
          {...args}
          open={open}
          onClose={() => setOpen(false)}
          action={undoAction}
        />
      </>
    );
  },
  args: {
    message: 'Item deleted',
    showCloseButton: false,
  },
};

export const Positions: Story = {
  render: () => {
    const [openStates, setOpenStates] = useState({
      topLeft: false,
      topCenter: false,
      topRight: false,
      bottomLeft: false,
      bottomCenter: false,
      bottomRight: false,
    });

    const positions = [
      { key: 'topLeft', label: 'Top Left', anchor: { vertical: 'top' as const, horizontal: 'left' as const } },
      { key: 'topCenter', label: 'Top Center', anchor: { vertical: 'top' as const, horizontal: 'center' as const } },
      { key: 'topRight', label: 'Top Right', anchor: { vertical: 'top' as const, horizontal: 'right' as const } },
      { key: 'bottomLeft', label: 'Bottom Left', anchor: { vertical: 'bottom' as const, horizontal: 'left' as const } },
      { key: 'bottomCenter', label: 'Bottom Center', anchor: { vertical: 'bottom' as const, horizontal: 'center' as const } },
      { key: 'bottomRight', label: 'Bottom Right', anchor: { vertical: 'bottom' as const, horizontal: 'right' as const } },
    ];

    const handleOpen = (key: keyof typeof openStates) => {
      setOpenStates(prev => ({ ...prev, [key]: true }));
    };

    const handleClose = (key: keyof typeof openStates) => {
      setOpenStates(prev => ({ ...prev, [key]: false }));
    };

    return (
      <>
        <Stack spacing={2}>
          {positions.map(({ key, label, anchor }) => (
            <Button 
              key={key}
              variant="outlined" 
              onClick={() => handleOpen(key as keyof typeof openStates)}
            >
              {label}
            </Button>
          ))}
        </Stack>

        {positions.map(({ key, label, anchor }) => (
          <MuiSnackbar
            key={key}
            open={openStates[key as keyof typeof openStates]}
            onClose={() => handleClose(key as keyof typeof openStates)}
            message={`Snackbar at ${label}`}
            anchorOrigin={anchor}
            autoHideDuration={3000}
          />
        ))}
      </>
    );
  },
};

export const NoAutoHide: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false);
    
    return (
      <>
        <Button variant="outlined" onClick={() => setOpen(true)}>
          Show Persistent Snackbar
        </Button>
        <MuiSnackbar
          {...args}
          open={open}
          onClose={() => setOpen(false)}
        />
      </>
    );
  },
  args: {
    message: 'This snackbar will not auto-hide',
    autoHideDuration: null,
  },
};
