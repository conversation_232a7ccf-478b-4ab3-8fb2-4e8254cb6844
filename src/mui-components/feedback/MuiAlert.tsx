import React from 'react';
import { Alert, AlertProps, AlertTitle, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

export interface MuiAlertProps extends AlertProps {
  /**
   * The title of the alert
   */
  title?: string;
  /**
   * The message content of the alert
   */
  message?: string;
  /**
   * Whether to show a close button
   */
  closable?: boolean;
  /**
   * Callback when close button is clicked
   */
  onClose?: () => void;
  /**
   * The severity of the alert
   */
  severity?: 'error' | 'warning' | 'info' | 'success';
  /**
   * The variant of the alert
   */
  variant?: 'filled' | 'outlined' | 'standard';
}

/**
 * A Material-UI Alert component for displaying important messages
 */
export const MuiAlert: React.FC<MuiAlertProps> = ({
  title,
  message,
  closable = false,
  onClose,
  severity = 'info',
  variant = 'standard',
  children,
  ...props
}) => {
  const action = closable ? (
    <IconButton
      aria-label="close"
      color="inherit"
      size="small"
      onClick={onClose}
    >
      <CloseIcon fontSize="inherit" />
    </IconButton>
  ) : undefined;

  return (
    <Alert
      severity={severity}
      variant={variant}
      action={action}
      {...props}
    >
      {title && <AlertTitle>{title}</AlertTitle>}
      {message || children}
    </Alert>
  );
};
