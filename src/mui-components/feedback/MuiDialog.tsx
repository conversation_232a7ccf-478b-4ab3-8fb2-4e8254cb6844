import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogProps,
  Button,
  IconButton,
  Typography,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

export interface MuiDialogProps extends Omit<DialogProps, 'title'> {
  /**
   * The title of the dialog
   */
  title?: string;
  /**
   * The content of the dialog
   */
  content?: React.ReactNode;
  /**
   * Whether to show a close button in the title
   */
  showCloseButton?: boolean;
  /**
   * Primary action button text
   */
  primaryAction?: string;
  /**
   * Secondary action button text
   */
  secondaryAction?: string;
  /**
   * Callback for primary action
   */
  onPrimaryAction?: () => void;
  /**
   * Callback for secondary action
   */
  onSecondaryAction?: () => void;
  /**
   * Callback for close action
   */
  onClose?: () => void;
  /**
   * Maximum width of the dialog
   */
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false;
}

/**
 * A Material-UI Dialog component for modal interactions
 */
export const MuiDialog: React.FC<MuiDialogProps> = ({
  title,
  content,
  showCloseButton = true,
  primaryAction,
  secondaryAction,
  onPrimaryAction,
  onSecondaryAction,
  onClose,
  maxWidth = 'sm',
  children,
  ...props
}) => {
  return (
    <Dialog
      maxWidth={maxWidth}
      fullWidth
      onClose={onClose}
      {...props}
    >
      {title && (
        <DialogTitle sx={{ m: 0, p: 2 }}>
          <Typography variant="h6" component="div">
            {title}
          </Typography>
          {showCloseButton && onClose && (
            <IconButton
              aria-label="close"
              onClick={onClose}
              sx={{
                position: 'absolute',
                right: 8,
                top: 8,
                color: (theme) => theme.palette.grey[500],
              }}
            >
              <CloseIcon />
            </IconButton>
          )}
        </DialogTitle>
      )}
      
      <DialogContent dividers>
        {content || children}
      </DialogContent>
      
      {(primaryAction || secondaryAction) && (
        <DialogActions>
          {secondaryAction && (
            <Button onClick={onSecondaryAction} color="inherit">
              {secondaryAction}
            </Button>
          )}
          {primaryAction && (
            <Button onClick={onPrimaryAction} variant="contained">
              {primaryAction}
            </Button>
          )}
        </DialogActions>
      )}
    </Dialog>
  );
};
