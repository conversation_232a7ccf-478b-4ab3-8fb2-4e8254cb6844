import React from 'react';
import { Backdrop, BackdropProps, CircularProgress } from '@mui/material';

export interface MuiBackdropProps extends BackdropProps {
  /**
   * Whether the backdrop is open
   */
  open: boolean;
  /**
   * Callback when backdrop is clicked
   */
  onClose?: () => void;
  /**
   * Whether to show a loading spinner
   */
  showSpinner?: boolean;
  /**
   * Custom content to display
   */
  children?: React.ReactNode;
  /**
   * Z-index of the backdrop
   */
  zIndex?: number;
}

/**
 * A Material-UI Backdrop component for overlaying content
 */
export const MuiBackdrop: React.FC<MuiBackdropProps> = ({
  open,
  onClose,
  showSpinner = true,
  children,
  zIndex = 1300,
  sx,
  ...props
}) => {
  return (
    <Backdrop
      sx={{
        color: '#fff',
        zIndex,
        ...sx,
      }}
      open={open}
      onClick={onClose}
      {...props}
    >
      {children || (showSpinner && <CircularProgress color="inherit" />)}
    </Backdrop>
  );
};
