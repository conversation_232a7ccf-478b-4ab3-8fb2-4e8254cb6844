import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Mui<PERSON>ackdrop } from './MuiBackdrop';
import { useState } from 'react';
import { Button, Typography, Box } from '@mui/material';

const meta: Meta<typeof MuiBackdrop> = {
  title: 'MUI Components/Feedback/Backdrop',
  component: MuiBackdrop,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Backdrop component for overlaying content and blocking user interaction.',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false);
    
    return (
      <>
        <Button variant="outlined" onClick={() => setOpen(true)}>
          Show Backdrop
        </Button>
        <MuiBackdrop
          {...args}
          open={open}
          onClose={() => setOpen(false)}
        />
      </>
    );
  },
  args: {},
};

export const WithoutSpinner: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false);
    
    return (
      <>
        <Button variant="outlined" onClick={() => setOpen(true)}>
          Show Backdrop (No Spinner)
        </Button>
        <MuiBackdrop
          {...args}
          open={open}
          onClose={() => setOpen(false)}
        />
      </>
    );
  },
  args: {
    showSpinner: false,
  },
};

export const WithCustomContent: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false);
    
    return (
      <>
        <Button variant="outlined" onClick={() => setOpen(true)}>
          Show Custom Backdrop
        </Button>
        <MuiBackdrop
          {...args}
          open={open}
          onClose={() => setOpen(false)}
        >
          <Box textAlign="center">
            <Typography variant="h4" gutterBottom>
              Loading...
            </Typography>
            <Typography variant="body1">
              Please wait while we process your request
            </Typography>
          </Box>
        </MuiBackdrop>
      </>
    );
  },
  args: {
    showSpinner: false,
  },
};

export const NonClosable: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false);
    
    return (
      <>
        <Button variant="outlined" onClick={() => setOpen(true)}>
          Show Non-Closable Backdrop
        </Button>
        {open && (
          <Button 
            variant="contained" 
            onClick={() => setOpen(false)}
            sx={{ ml: 2 }}
          >
            Close Programmatically
          </Button>
        )}
        <MuiBackdrop
          {...args}
          open={open}
        />
      </>
    );
  },
  args: {
    onClose: undefined,
  },
};

export const CustomZIndex: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false);
    
    return (
      <>
        <Button variant="outlined" onClick={() => setOpen(true)}>
          Show High Z-Index Backdrop
        </Button>
        <MuiBackdrop
          {...args}
          open={open}
          onClose={() => setOpen(false)}
        />
      </>
    );
  },
  args: {
    zIndex: 2000,
  },
};
