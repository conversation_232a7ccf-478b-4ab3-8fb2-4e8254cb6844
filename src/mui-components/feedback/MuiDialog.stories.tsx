import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { MuiDialog } from './MuiDialog';
import { useState } from 'react';
import { Button, TextField, Typography } from '@mui/material';

const meta: Meta<typeof MuiDialog> = {
  title: 'MUI Components/Feedback/Dialog',
  component: MuiDialog,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Dialog component for modal interactions and confirmations.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    maxWidth: {
      control: { type: 'select' },
      options: ['xs', 'sm', 'md', 'lg', 'xl', false],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false);
    
    return (
      <>
        <Button variant="outlined" onClick={() => setOpen(true)}>
          Open Dialog
        </Button>
        <MuiDialog
          {...args}
          open={open}
          onClose={() => setOpen(false)}
        />
      </>
    );
  },
  args: {
    title: 'Dialog Title',
    content: 'This is the dialog content. You can put any content here.',
    primaryAction: 'Confirm',
    secondaryAction: 'Cancel',
  },
};

export const ConfirmationDialog: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false);
    
    return (
      <>
        <Button variant="outlined" color="error" onClick={() => setOpen(true)}>
          Delete Item
        </Button>
        <MuiDialog
          {...args}
          open={open}
          onClose={() => setOpen(false)}
          onPrimaryAction={() => {
            alert('Item deleted!');
            setOpen(false);
          }}
          onSecondaryAction={() => setOpen(false)}
        />
      </>
    );
  },
  args: {
    title: 'Confirm Deletion',
    content: 'Are you sure you want to delete this item? This action cannot be undone.',
    primaryAction: 'Delete',
    secondaryAction: 'Cancel',
    maxWidth: 'xs',
  },
};

export const FormDialog: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false);
    const [email, setEmail] = useState('');
    
    return (
      <>
        <Button variant="outlined" onClick={() => setOpen(true)}>
          Subscribe
        </Button>
        <MuiDialog
          {...args}
          open={open}
          onClose={() => setOpen(false)}
          onPrimaryAction={() => {
            alert(`Subscribed with email: ${email}`);
            setOpen(false);
            setEmail('');
          }}
          onSecondaryAction={() => {
            setOpen(false);
            setEmail('');
          }}
        >
          <Typography gutterBottom>
            To subscribe to this website, please enter your email address here.
          </Typography>
          <TextField
            autoFocus
            margin="dense"
            label="Email Address"
            type="email"
            fullWidth
            variant="outlined"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
        </MuiDialog>
      </>
    );
  },
  args: {
    title: 'Subscribe',
    primaryAction: 'Subscribe',
    secondaryAction: 'Cancel',
  },
};

export const FullScreenDialog: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false);
    
    return (
      <>
        <Button variant="outlined" onClick={() => setOpen(true)}>
          Open Full Screen
        </Button>
        <MuiDialog
          {...args}
          open={open}
          onClose={() => setOpen(false)}
          fullScreen
        />
      </>
    );
  },
  args: {
    title: 'Full Screen Dialog',
    content: 'This dialog takes up the full screen. It\'s useful for mobile devices or when you need more space.',
    primaryAction: 'Save',
    secondaryAction: 'Cancel',
  },
};

export const NoActions: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false);
    
    return (
      <>
        <Button variant="outlined" onClick={() => setOpen(true)}>
          Open Info Dialog
        </Button>
        <MuiDialog
          {...args}
          open={open}
          onClose={() => setOpen(false)}
        />
      </>
    );
  },
  args: {
    title: 'Information',
    content: 'This is an informational dialog with no action buttons. You can close it using the X button or by clicking outside.',
  },
};
