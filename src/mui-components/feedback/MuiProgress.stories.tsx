import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { MuiCircularProgress, MuiLinearProgress } from './MuiProgress';
import { useState, useEffect } from 'react';
import { Stack, Button, Box } from '@mui/material';

const meta: Meta<typeof MuiCircularProgress> = {
  title: 'MUI Components/Feedback/Progress',
  component: MuiCircularProgress,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Material-UI Progress components for showing loading states and progress indicators.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    color: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'error', 'info', 'success', 'warning', 'inherit'],
    },
    variant: {
      control: { type: 'select' },
      options: ['determinate', 'indeterminate'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof MuiCircularProgress>;

// Circular Progress Stories
export const CircularIndeterminate: Story = {
  args: {
    variant: 'indeterminate',
  },
};

export const CircularDeterminate: Story = {
  args: {
    variant: 'determinate',
    value: 75,
    showValue: true,
  },
};

export const CircularWithLabel: Story = {
  args: {
    variant: 'determinate',
    value: 60,
    label: 'Loading...',
  },
};

export const CircularSizes: Story = {
  render: () => (
    <Stack direction="row" spacing={2} alignItems="center">
      <MuiCircularProgress size={20} />
      <MuiCircularProgress size={40} />
      <MuiCircularProgress size={60} />
      <MuiCircularProgress size={80} />
    </Stack>
  ),
};

export const CircularColors: Story = {
  render: () => (
    <Stack direction="row" spacing={2} alignItems="center">
      <MuiCircularProgress color="primary" />
      <MuiCircularProgress color="secondary" />
      <MuiCircularProgress color="success" />
      <MuiCircularProgress color="error" />
      <MuiCircularProgress color="warning" />
      <MuiCircularProgress color="info" />
    </Stack>
  ),
};

export const CircularAnimated: Story = {
  render: () => {
    const [progress, setProgress] = useState(0);

    useEffect(() => {
      const timer = setInterval(() => {
        setProgress((prevProgress) => (prevProgress >= 100 ? 0 : prevProgress + 10));
      }, 800);

      return () => {
        clearInterval(timer);
      };
    }, []);

    return (
      <MuiCircularProgress
        variant="determinate"
        value={progress}
        showValue
      />
    );
  },
};

// Linear Progress Stories
export const LinearIndeterminate: Story = {
  render: () => (
    <Box sx={{ width: 400 }}>
      <MuiLinearProgress variant="indeterminate" />
    </Box>
  ),
};

export const LinearDeterminate: Story = {
  render: () => (
    <Box sx={{ width: 400 }}>
      <MuiLinearProgress
        variant="determinate"
        value={75}
        showValue
      />
    </Box>
  ),
};

export const LinearWithLabel: Story = {
  render: () => (
    <Box sx={{ width: 400 }}>
      <MuiLinearProgress
        variant="determinate"
        value={60}
        label="Uploading file..."
        showValue
      />
    </Box>
  ),
};

export const LinearBuffer: Story = {
  render: () => {
    const [progress, setProgress] = useState(0);
    const [buffer, setBuffer] = useState(10);

    useEffect(() => {
      const timer = setInterval(() => {
        setProgress((oldProgress) => {
          if (oldProgress === 100) {
            return 0;
          }
          const diff = Math.random() * 10;
          return Math.min(oldProgress + diff, 100);
        });
      }, 500);

      return () => {
        clearInterval(timer);
      };
    }, []);

    useEffect(() => {
      const timer = setInterval(() => {
        setBuffer((oldBuffer) => {
          if (oldBuffer === 100) {
            return 10;
          }
          const diff = Math.random() * 10;
          return Math.min(oldBuffer + diff, 100);
        });
      }, 500);

      return () => {
        clearInterval(timer);
      };
    }, []);

    return (
      <Box sx={{ width: 400 }}>
        <MuiLinearProgress
          variant="buffer"
          value={progress}
          valueBuffer={buffer}
          showValue
        />
      </Box>
    );
  },
};

export const LinearColors: Story = {
  render: () => (
    <Stack spacing={2} sx={{ width: 400 }}>
      <MuiLinearProgress color="primary" value={50} variant="determinate" />
      <MuiLinearProgress color="secondary" value={60} variant="determinate" />
      <MuiLinearProgress color="success" value={70} variant="determinate" />
      <MuiLinearProgress color="error" value={80} variant="determinate" />
      <MuiLinearProgress color="warning" value={90} variant="determinate" />
      <MuiLinearProgress color="info" value={40} variant="determinate" />
    </Stack>
  ),
};

export const LinearAnimated: Story = {
  render: () => {
    const [progress, setProgress] = useState(0);
    const [loading, setLoading] = useState(false);

    const startProgress = () => {
      setLoading(true);
      setProgress(0);
      const timer = setInterval(() => {
        setProgress((prevProgress) => {
          if (prevProgress >= 100) {
            clearInterval(timer);
            setLoading(false);
            return 100;
          }
          return prevProgress + Math.random() * 10;
        });
      }, 200);
    };

    return (
      <Box sx={{ width: 400 }}>
        <Button 
          variant="outlined" 
          onClick={startProgress} 
          disabled={loading}
          sx={{ mb: 2 }}
        >
          {loading ? 'Loading...' : 'Start Progress'}
        </Button>
        <MuiLinearProgress
          variant="determinate"
          value={progress}
          label="Processing..."
          showValue
        />
      </Box>
    );
  },
};
