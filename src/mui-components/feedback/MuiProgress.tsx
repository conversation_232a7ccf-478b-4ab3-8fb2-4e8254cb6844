import React from 'react';
import {
  CircularProgress,
  LinearProgress,
  CircularProgressProps,
  LinearProgressProps,
  Box,
  Typography,
} from '@mui/material';

export interface MuiCircularProgressProps extends CircularProgressProps {
  /**
   * The variant of the progress indicator
   */
  variant?: 'determinate' | 'indeterminate';
  /**
   * The value of the progress indicator (0-100)
   */
  value?: number;
  /**
   * Whether to show the value as text
   */
  showValue?: boolean;
  /**
   * Custom label to display
   */
  label?: string;
  /**
   * Size of the progress indicator
   */
  size?: number | string;
}

export interface MuiLinearProgressProps extends LinearProgressProps {
  /**
   * The variant of the progress indicator
   */
  variant?: 'determinate' | 'indeterminate' | 'buffer' | 'query';
  /**
   * The value of the progress indicator (0-100)
   */
  value?: number;
  /**
   * The value of the buffer indicator (0-100)
   */
  valueBuffer?: number;
  /**
   * Whether to show the value as text
   */
  showValue?: boolean;
  /**
   * Custom label to display
   */
  label?: string;
}

/**
 * A Material-UI Circular Progress component for showing loading states
 */
export const MuiCircularProgress: React.FC<MuiCircularProgressProps> = ({
  variant = 'indeterminate',
  value,
  showValue = false,
  label,
  size = 40,
  color = 'primary',
  ...props
}) => {
  return (
    <Box sx={{ position: 'relative', display: 'inline-flex' }}>
      <CircularProgress
        variant={variant}
        value={value}
        size={size}
        color={color}
        {...props}
      />
      {(showValue || label) && (
        <Box
          sx={{
            top: 0,
            left: 0,
            bottom: 0,
            right: 0,
            position: 'absolute',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Typography
            variant="caption"
            component="div"
            color="text.secondary"
          >
            {label || (showValue && value !== undefined ? `${Math.round(value)}%` : '')}
          </Typography>
        </Box>
      )}
    </Box>
  );
};

/**
 * A Material-UI Linear Progress component for showing loading states
 */
export const MuiLinearProgress: React.FC<MuiLinearProgressProps> = ({
  variant = 'indeterminate',
  value,
  valueBuffer,
  showValue = false,
  label,
  color = 'primary',
  sx,
  ...props
}) => {
  return (
    <Box sx={{ width: '100%', ...sx }}>
      {(label || showValue) && (
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Box sx={{ width: '100%', mr: 1 }}>
            <Typography variant="body2" color="text.secondary">
              {label || 'Loading...'}
            </Typography>
          </Box>
          {showValue && value !== undefined && (
            <Box sx={{ minWidth: 35 }}>
              <Typography variant="body2" color="text.secondary">
                {`${Math.round(value)}%`}
              </Typography>
            </Box>
          )}
        </Box>
      )}
      <LinearProgress
        variant={variant}
        value={value}
        valueBuffer={valueBuffer}
        color={color}
        {...props}
      />
    </Box>
  );
};
