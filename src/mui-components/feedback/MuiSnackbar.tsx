import React from 'react';
import {
  Snackbar,
  Alert,
  SnackbarProps,
  AlertProps,
  IconButton,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

export interface MuiSnackbarProps extends Omit<SnackbarProps, 'message'> {
  /**
   * Whether the snackbar is open
   */
  open: boolean;
  /**
   * The message to display
   */
  message?: string;
  /**
   * Callback when snackbar should close
   */
  onClose?: (event?: React.SyntheticEvent | Event, reason?: string) => void;
  /**
   * Auto hide duration in milliseconds
   */
  autoHideDuration?: number;
  /**
   * Severity level for alert variant
   */
  severity?: 'error' | 'warning' | 'info' | 'success';
  /**
   * Whether to use Alert component instead of plain message
   */
  useAlert?: boolean;
  /**
   * Whether to show close button
   */
  showCloseButton?: boolean;
  /**
   * Alert variant
   */
  alertVariant?: AlertProps['variant'];
  /**
   * Custom action element
   */
  action?: React.ReactNode;
}

/**
 * A Material-UI Snackbar component for showing brief messages
 */
export const MuiSnackbar: React.FC<MuiSnackbarProps> = ({
  open,
  message,
  onClose,
  autoHideDuration = 6000,
  severity,
  useAlert = false,
  showCloseButton = true,
  alertVariant = 'filled',
  action,
  anchorOrigin = {
    vertical: 'bottom',
    horizontal: 'left',
  },
  ...props
}) => {
  const handleClose = (event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    onClose?.(event, reason);
  };

  const closeAction = showCloseButton ? (
    <IconButton
      size="small"
      aria-label="close"
      color="inherit"
      onClick={handleClose}
    >
      <CloseIcon fontSize="small" />
    </IconButton>
  ) : undefined;

  if (useAlert && severity) {
    return (
      <Snackbar
        open={open}
        autoHideDuration={autoHideDuration}
        onClose={handleClose}
        anchorOrigin={anchorOrigin}
        {...props}
      >
        <Alert
          onClose={handleClose}
          severity={severity}
          variant={alertVariant}
          sx={{ width: '100%' }}
        >
          {message}
        </Alert>
      </Snackbar>
    );
  }

  return (
    <Snackbar
      open={open}
      autoHideDuration={autoHideDuration}
      onClose={handleClose}
      message={message}
      action={action || closeAction}
      anchorOrigin={anchorOrigin}
      {...props}
    />
  );
};
