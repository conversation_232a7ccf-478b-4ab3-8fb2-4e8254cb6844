import type { Meta, StoryObj } from '@storybook/react';
import { Mui<PERSON>lert } from './MuiAlert';
import { useState } from 'react';
import { Stack } from '@mui/material';

const meta: Meta<typeof MuiAlert> = {
  title: 'MUI Components/Feedback/Alert',
  component: MuiAlert,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Alert component for displaying important messages to users.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    severity: {
      control: { type: 'select' },
      options: ['error', 'warning', 'info', 'success'],
    },
    variant: {
      control: { type: 'select' },
      options: ['filled', 'outlined', 'standard'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    message: 'This is an info alert — check it out!',
  },
};

export const WithTitle: Story = {
  args: {
    title: 'Info',
    message: 'This is an info alert with a title — check it out!',
    severity: 'info',
  },
};

export const Closable: Story = {
  render: (args) => {
    const [open, setOpen] = useState(true);
    
    if (!open) {
      return (
        <button onClick={() => setOpen(true)}>
          Show Alert Again
        </button>
      );
    }
    
    return (
      <MuiAlert
        {...args}
        closable
        onClose={() => setOpen(false)}
      />
    );
  },
  args: {
    title: 'Closable Alert',
    message: 'You can close this alert by clicking the X button.',
    severity: 'warning',
  },
};

export const Severities: Story = {
  render: () => (
    <Stack spacing={2} sx={{ width: '100%', maxWidth: 600 }}>
      <MuiAlert severity="error" message="This is an error alert — check it out!" />
      <MuiAlert severity="warning" message="This is a warning alert — check it out!" />
      <MuiAlert severity="info" message="This is an info alert — check it out!" />
      <MuiAlert severity="success" message="This is a success alert — check it out!" />
    </Stack>
  ),
};

export const Variants: Story = {
  render: () => (
    <Stack spacing={2} sx={{ width: '100%', maxWidth: 600 }}>
      <MuiAlert 
        severity="success" 
        variant="filled" 
        message="This is a filled success alert — check it out!" 
      />
      <MuiAlert 
        severity="success" 
        variant="outlined" 
        message="This is an outlined success alert — check it out!" 
      />
      <MuiAlert 
        severity="success" 
        variant="standard" 
        message="This is a standard success alert — check it out!" 
      />
    </Stack>
  ),
};

export const WithTitleAndClose: Story = {
  args: {
    title: 'Success!',
    message: 'Your changes have been saved successfully.',
    severity: 'success',
    closable: true,
  },
};
