import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MuiCheckbox } from './MuiCheckbox';
import { useState } from 'react';

const meta: Meta<typeof MuiCheckbox> = {
  title: 'MUI Components/Inputs/Checkbox',
  component: MuiCheckbox,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Checkbox component with optional label and customization.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    color: {
      control: { type: 'select' },
      options: ['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning'],
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large'],
    },
    labelPlacement: {
      control: { type: 'select' },
      options: ['end', 'start', 'top', 'bottom'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    label: 'Default checkbox',
  },
};

export const WithoutLabel: Story = {
  args: {
    showLabel: false,
  },
};

export const Controlled: Story = {
  render: (args) => {
    const [checked, setChecked] = useState(false);
    return (
      <MuiCheckbox
        {...args}
        checked={checked}
        onChange={(e) => setChecked(e.target.checked)}
      />
    );
  },
  args: {
    label: 'Controlled checkbox',
  },
};

export const Indeterminate: Story = {
  args: {
    label: 'Indeterminate checkbox',
    indeterminate: true,
  },
};

export const Disabled: Story = {
  args: {
    label: 'Disabled checkbox',
    disabled: true,
  },
};

export const Colors: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
      <MuiCheckbox label="Primary" color="primary" defaultChecked />
      <MuiCheckbox label="Secondary" color="secondary" defaultChecked />
      <MuiCheckbox label="Success" color="success" defaultChecked />
      <MuiCheckbox label="Error" color="error" defaultChecked />
      <MuiCheckbox label="Warning" color="warning" defaultChecked />
    </div>
  ),
};
