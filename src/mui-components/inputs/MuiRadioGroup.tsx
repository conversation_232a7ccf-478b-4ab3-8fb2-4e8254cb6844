import React from 'react';
import {
  RadioGroup,
  FormControlLabel,
  Radio,
  FormControl,
  FormLabel,
  RadioGroupProps,
} from '@mui/material';

export interface RadioOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface MuiRadioGroupProps extends Omit<RadioGroupProps, 'children'> {
  /**
   * The label for the radio group
   */
  label?: string;
  /**
   * Array of radio options
   */
  options: RadioOption[];
  /**
   * Color of the radio buttons
   */
  color?: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  /**
   * Size of the radio buttons
   */
  size?: 'small' | 'medium';
}

/**
 * A Material-UI RadioGroup component with customizable options
 */
export const MuiRadioGroup: React.FC<MuiRadioGroupProps> = ({
  label,
  options,
  color = 'primary',
  size = 'medium',
  row = false,
  ...props
}) => {
  return (
    <FormControl component="fieldset">
      {label && <FormLabel component="legend">{label}</FormLabel>}
      <RadioGroup row={row} {...props}>
        {options.map((option) => (
          <FormControlLabel
            key={option.value}
            value={option.value}
            control={<Radio color={color} size={size} />}
            label={option.label}
            disabled={option.disabled}
          />
        ))}
      </RadioGroup>
    </FormControl>
  );
};
