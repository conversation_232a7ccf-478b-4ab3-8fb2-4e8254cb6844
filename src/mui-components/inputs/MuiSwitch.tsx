import React from 'react';
import { Switch, FormControlLabel, FormGroup, SwitchProps } from '@mui/material';

export interface MuiSwitchProps extends SwitchProps {
  /**
   * The label for the switch
   */
  label?: string;
  /**
   * Whether to show the label
   */
  showLabel?: boolean;
  /**
   * Label placement
   */
  labelPlacement?: 'end' | 'start' | 'top' | 'bottom';
  /**
   * Custom labels for on/off states
   */
  onLabel?: string;
  /**
   * Custom label for off state
   */
  offLabel?: string;
}

/**
 * A Material-UI Switch component for boolean input
 */
export const MuiSwitch: React.FC<MuiSwitchProps> = ({
  label,
  showLabel = true,
  labelPlacement = 'end',
  onLabel,
  offLabel,
  checked,
  ...props
}) => {
  const getDisplayLabel = () => {
    if (onLabel && offLabel) {
      return checked ? onLabel : offLabel;
    }
    return label;
  };

  if (!showLabel || !label) {
    return <Switch checked={checked} {...props} />;
  }

  return (
    <FormGroup>
      <FormControlLabel
        control={<Switch checked={checked} {...props} />}
        label={getDisplayLabel()}
        labelPlacement={labelPlacement}
      />
    </FormGroup>
  );
};
