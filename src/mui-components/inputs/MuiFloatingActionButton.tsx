import React from 'react';
import { Fab, FabProps } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';

export interface MuiFloatingActionButtonProps extends FabProps {
  /**
   * The icon to display
   */
  icon?: React.ReactNode;
  /**
   * The text label (for extended FAB)
   */
  label?: string;
  /**
   * Whether to show as extended FAB with text
   */
  extended?: boolean;
}

/**
 * A Material-UI Floating Action Button component
 */
export const MuiFloatingActionButton: React.FC<MuiFloatingActionButtonProps> = ({
  icon = <AddIcon />,
  label,
  extended = false,
  variant = 'circular',
  color = 'primary',
  size = 'large',
  ...props
}) => {
  if (extended && label) {
    return (
      <Fab
        variant="extended"
        color={color}
        size={size}
        {...props}
      >
        {icon}
        {label}
      </Fab>
    );
  }

  return (
    <Fab
      variant={variant}
      color={color}
      size={size}
      {...props}
    >
      {icon}
    </Fab>
  );
};
