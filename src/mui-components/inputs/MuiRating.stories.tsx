import type { Meta, StoryObj } from '@storybook/react';
import { MuiRating } from './MuiRating';
import { useState } from 'react';
import FavoriteIcon from '@mui/icons-material/Favorite';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';

const meta: Meta<typeof MuiRating> = {
  title: 'MUI Components/Inputs/Rating',
  component: MuiRating,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Rating component for collecting user feedback.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large'],
    },
    precision: {
      control: { type: 'number', min: 0.1, max: 1, step: 0.1 },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    label: 'Rate this product',
    value: 3,
    showValue: true,
  },
};

export const Controlled: Story = {
  render: (args) => {
    const [value, setValue] = useState<number | null>(2);
    return (
      <MuiRating
        {...args}
        value={value}
        onChange={(event, newValue) => setValue(newValue)}
      />
    );
  },
  args: {
    label: 'Your rating',
    showValue: true,
  },
};

export const ReadOnly: Story = {
  args: {
    label: 'Average rating',
    value: 4.5,
    readOnly: true,
    precision: 0.5,
    showValue: true,
  },
};

export const WithLabels: Story = {
  args: {
    label: 'How was your experience?',
    value: 3,
    showValue: true,
    labels: {
      1: 'Terrible',
      2: 'Poor',
      3: 'Average',
      4: 'Good',
      5: 'Excellent',
    },
  },
};

export const HalfStars: Story = {
  args: {
    label: 'Precise rating',
    value: 3.5,
    precision: 0.5,
    showValue: true,
  },
};

export const CustomIcon: Story = {
  args: {
    label: 'Like this?',
    value: 3,
    max: 5,
    icon: <FavoriteIcon fontSize="inherit" />,
    emptyIcon: <FavoriteBorderIcon fontSize="inherit" />,
    showValue: true,
  },
};

export const Disabled: Story = {
  args: {
    label: 'Disabled rating',
    value: 2,
    disabled: true,
    showValue: true,
  },
};
