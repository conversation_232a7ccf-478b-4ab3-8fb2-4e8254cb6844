import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MuiFloatingActionButton } from './MuiFloatingActionButton';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import FavoriteIcon from '@mui/icons-material/Favorite';
import NavigationIcon from '@mui/icons-material/Navigation';

const meta: Meta<typeof MuiFloatingActionButton> = {
  title: 'MUI Components/Inputs/FloatingActionButton',
  component: MuiFloatingActionButton,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Floating Action Button for primary actions.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    color: {
      control: { type: 'select' },
      options: ['default', 'primary', 'secondary', 'inherit'],
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large'],
    },
    variant: {
      control: { type: 'select' },
      options: ['circular', 'extended'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const Extended: Story = {
  args: {
    extended: true,
    label: 'Add Item',
    icon: <AddIcon sx={{ mr: 1 }} />,
  },
};

export const WithEditIcon: Story = {
  args: {
    icon: <EditIcon />,
    color: 'secondary',
  },
};

export const Small: Story = {
  args: {
    size: 'small',
    icon: <FavoriteIcon />,
    color: 'secondary',
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
    icon: <NavigationIcon />,
  },
};

export const Colors: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
      <MuiFloatingActionButton color="primary" icon={<AddIcon />} />
      <MuiFloatingActionButton color="secondary" icon={<EditIcon />} />
      <MuiFloatingActionButton color="inherit" icon={<FavoriteIcon />} />
      <MuiFloatingActionButton color="default" icon={<NavigationIcon />} />
    </div>
  ),
};
