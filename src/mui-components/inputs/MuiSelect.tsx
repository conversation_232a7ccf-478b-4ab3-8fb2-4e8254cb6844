import React from 'react';
import {
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  FormHelperText,
  SelectProps,
  SelectChangeEvent,
} from '@mui/material';

export interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
}

export interface MuiSelectProps extends Omit<SelectProps, 'onChange'> {
  /**
   * The label for the select field
   */
  label?: string;
  /**
   * Array of select options
   */
  options: SelectOption[];
  /**
   * Helper text
   */
  helperText?: string;
  /**
   * Error state
   */
  error?: boolean;
  /**
   * Required field
   */
  required?: boolean;
  /**
   * Input variant
   */
  variant?: 'outlined' | 'filled' | 'standard';
  /**
   * Placeholder text when no value is selected
   */
  placeholder?: string;
  /**
   * Callback when value changes
   */
  onChange?: (event: SelectChangeEvent<unknown>, child: React.ReactNode) => void;
}

/**
 * A Material-UI Select component for choosing from a list of options
 */
export const MuiSelect: React.FC<MuiSelectProps> = ({
  label,
  options,
  helperText,
  error = false,
  required = false,
  variant = 'outlined',
  placeholder,
  onChange,
  value,
  ...props
}) => {
  const labelId = `select-label-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <FormControl variant={variant} error={error} required={required} fullWidth>
      {label && <InputLabel id={labelId}>{label}</InputLabel>}
      <Select
        labelId={label ? labelId : undefined}
        label={label}
        value={value}
        onChange={onChange}
        displayEmpty={!!placeholder}
        {...props}
      >
        {placeholder && (
          <MenuItem value="" disabled>
            <em>{placeholder}</em>
          </MenuItem>
        )}
        {options.map((option) => (
          <MenuItem
            key={option.value}
            value={option.value}
            disabled={option.disabled}
          >
            {option.label}
          </MenuItem>
        ))}
      </Select>
      {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </FormControl>
  );
};
