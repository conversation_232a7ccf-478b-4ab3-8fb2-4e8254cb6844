import type { Meta, StoryObj } from '@storybook/react';
import { MuiTransferList } from './MuiTransferList';
import { useState } from 'react';

const meta: Meta<typeof MuiTransferList> = {
  title: 'MUI Components/Inputs/TransferList',
  component: MuiTransferList,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Transfer List component for moving items between two lists.',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

const initialLeftItems = [
  { id: 1, label: 'Item 1' },
  { id: 2, label: 'Item 2' },
  { id: 3, label: 'Item 3' },
  { id: 4, label: 'Item 4' },
  { id: 5, label: 'Item 5' },
  { id: 6, label: 'Item 6' },
  { id: 7, label: 'Item 7' },
  { id: 8, label: 'Item 8' },
];

const initialRightItems = [
  { id: 9, label: 'Item 9' },
  { id: 10, label: 'Item 10' },
];

const userItems = [
  { id: 'user1', label: '<PERSON>' },
  { id: 'user2', label: '<PERSON>' },
  { id: 'user3', label: '<PERSON>' },
  { id: 'user4', label: '<PERSON>' },
  { id: 'user5', label: 'Eve Wilson' },
  { id: 'user6', label: 'Frank Miller' },
];

const permissionItems = [
  { id: 'read', label: 'Read Access' },
  { id: 'write', label: 'Write Access' },
  { id: 'delete', label: 'Delete Access' },
  { id: 'admin', label: 'Admin Access' },
];

export const Default: Story = {
  render: (args) => {
    const [left, setLeft] = useState(initialLeftItems);
    const [right, setRight] = useState(initialRightItems);

    const handleChange = (newLeft: any[], newRight: any[]) => {
      setLeft(newLeft);
      setRight(newRight);
    };

    return (
      <MuiTransferList
        {...args}
        leftItems={left}
        rightItems={right}
        onChange={handleChange}
      />
    );
  },
  args: {},
};

export const UserSelection: Story = {
  render: (args) => {
    const [availableUsers, setAvailableUsers] = useState(userItems);
    const [selectedUsers, setSelectedUsers] = useState([]);

    const handleChange = (newLeft: any[], newRight: any[]) => {
      setAvailableUsers(newLeft);
      setSelectedUsers(newRight);
    };

    return (
      <MuiTransferList
        {...args}
        leftItems={availableUsers}
        rightItems={selectedUsers}
        onChange={handleChange}
      />
    );
  },
  args: {
    leftTitle: 'Available Users',
    rightTitle: 'Selected Users',
  },
};

export const PermissionAssignment: Story = {
  render: (args) => {
    const [availablePermissions, setAvailablePermissions] = useState(permissionItems);
    const [assignedPermissions, setAssignedPermissions] = useState([]);

    const handleChange = (newLeft: any[], newRight: any[]) => {
      setAvailablePermissions(newLeft);
      setAssignedPermissions(newRight);
    };

    return (
      <MuiTransferList
        {...args}
        leftItems={availablePermissions}
        rightItems={assignedPermissions}
        onChange={handleChange}
      />
    );
  },
  args: {
    leftTitle: 'Available Permissions',
    rightTitle: 'Assigned Permissions',
  },
};

export const WithDisabledItems: Story = {
  render: (args) => {
    const itemsWithDisabled = [
      { id: 1, label: 'Item 1' },
      { id: 2, label: 'Item 2 (Disabled)', disabled: true },
      { id: 3, label: 'Item 3' },
      { id: 4, label: 'Item 4' },
      { id: 5, label: 'Item 5 (Disabled)', disabled: true },
    ];

    const [left, setLeft] = useState(itemsWithDisabled);
    const [right, setRight] = useState([]);

    const handleChange = (newLeft: any[], newRight: any[]) => {
      setLeft(newLeft);
      setRight(newRight);
    };

    return (
      <MuiTransferList
        {...args}
        leftItems={left}
        rightItems={right}
        onChange={handleChange}
      />
    );
  },
  args: {
    leftTitle: 'Available Items',
    rightTitle: 'Selected Items',
  },
};

export const Disabled: Story = {
  render: (args) => {
    const [left, setLeft] = useState(initialLeftItems);
    const [right, setRight] = useState(initialRightItems);

    const handleChange = (newLeft: any[], newRight: any[]) => {
      setLeft(newLeft);
      setRight(newRight);
    };

    return (
      <MuiTransferList
        {...args}
        leftItems={left}
        rightItems={right}
        onChange={handleChange}
      />
    );
  },
  args: {
    disabled: true,
    leftTitle: 'Available Items',
    rightTitle: 'Selected Items',
  },
};
