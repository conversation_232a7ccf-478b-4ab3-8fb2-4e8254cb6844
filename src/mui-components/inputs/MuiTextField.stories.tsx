import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MuiTextField } from './MuiTextField';
import { useState } from 'react';
import { Stack } from '@mui/material';

const meta: Meta<typeof MuiTextField> = {
  title: 'MUI Components/Inputs/TextField',
  component: MuiTextField,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI TextField component for text input with various configurations.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['outlined', 'filled', 'standard'],
    },
    type: {
      control: { type: 'select' },
      options: ['text', 'password', 'email', 'number', 'tel', 'url', 'search'],
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    label: 'Default TextField',
    placeholder: 'Enter text here...',
  },
};

export const Controlled: Story = {
  render: (args) => {
    const [value, setValue] = useState('');
    return (
      <MuiTextField
        {...args}
        value={value}
        onChange={(e) => setValue(e.target.value)}
      />
    );
  },
  args: {
    label: 'Controlled TextField',
    helperText: 'This field is controlled by React state',
  },
};

export const WithError: Story = {
  args: {
    label: 'Email',
    type: 'email',
    error: true,
    helperText: 'Please enter a valid email address',
    value: 'invalid-email',
  },
};

export const Required: Story = {
  args: {
    label: 'Required Field',
    required: true,
    helperText: 'This field is required',
  },
};

export const Multiline: Story = {
  args: {
    label: 'Multiline TextField',
    multiline: true,
    rows: 4,
    placeholder: 'Enter multiple lines of text...',
    helperText: 'You can enter multiple lines here',
  },
};

export const Password: Story = {
  args: {
    label: 'Password',
    type: 'password',
    helperText: 'Enter your password',
  },
};

export const Variants: Story = {
  render: () => (
    <Stack spacing={3} sx={{ width: 300 }}>
      <MuiTextField
        label="Outlined (default)"
        variant="outlined"
        placeholder="Outlined variant"
      />
      <MuiTextField
        label="Filled"
        variant="filled"
        placeholder="Filled variant"
      />
      <MuiTextField
        label="Standard"
        variant="standard"
        placeholder="Standard variant"
      />
    </Stack>
  ),
};

export const Sizes: Story = {
  render: () => (
    <Stack spacing={2} sx={{ width: 300 }}>
      <MuiTextField
        label="Small"
        size="small"
        placeholder="Small size"
      />
      <MuiTextField
        label="Medium (default)"
        size="medium"
        placeholder="Medium size"
      />
    </Stack>
  ),
};

export const Disabled: Story = {
  args: {
    label: 'Disabled TextField',
    disabled: true,
    value: 'This field is disabled',
    helperText: 'This field cannot be edited',
  },
};
