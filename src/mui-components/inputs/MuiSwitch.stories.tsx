import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MuiSwitch } from './MuiSwitch';
import { useState } from 'react';
import { Stack, Typography } from '@mui/material';

const meta: Meta<typeof MuiSwitch> = {
  title: 'MUI Components/Inputs/Switch',
  component: MuiSwitch,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Switch component for boolean input and toggle functionality.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    color: {
      control: { type: 'select' },
      options: ['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning'],
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium'],
    },
    labelPlacement: {
      control: { type: 'select' },
      options: ['end', 'start', 'top', 'bottom'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    label: 'Enable notifications',
    defaultChecked: true,
  },
};

export const WithoutLabel: Story = {
  args: {
    showLabel: false,
    defaultChecked: false,
  },
};

export const Controlled: Story = {
  render: (args) => {
    const [checked, setChecked] = useState(false);
    return (
      <div>
        <MuiSwitch
          {...args}
          checked={checked}
          onChange={(e) => setChecked(e.target.checked)}
        />
        <Typography variant="body2" sx={{ mt: 1 }}>
          Switch is {checked ? 'ON' : 'OFF'}
        </Typography>
      </div>
    );
  },
  args: {
    label: 'Controlled switch',
  },
};

export const WithCustomLabels: Story = {
  render: (args) => {
    const [checked, setChecked] = useState(false);
    return (
      <MuiSwitch
        {...args}
        checked={checked}
        onChange={(e) => setChecked(e.target.checked)}
      />
    );
  },
  args: {
    label: 'Status',
    onLabel: 'Online',
    offLabel: 'Offline',
  },
};

export const Disabled: Story = {
  args: {
    label: 'Disabled switch',
    disabled: true,
    checked: true,
  },
};

export const Colors: Story = {
  render: () => (
    <Stack spacing={2}>
      <MuiSwitch label="Primary (default)" color="primary" defaultChecked />
      <MuiSwitch label="Secondary" color="secondary" defaultChecked />
      <MuiSwitch label="Success" color="success" defaultChecked />
      <MuiSwitch label="Error" color="error" defaultChecked />
      <MuiSwitch label="Warning" color="warning" defaultChecked />
      <MuiSwitch label="Info" color="info" defaultChecked />
    </Stack>
  ),
};

export const Sizes: Story = {
  render: () => (
    <Stack spacing={2}>
      <MuiSwitch label="Small" size="small" defaultChecked />
      <MuiSwitch label="Medium (default)" size="medium" defaultChecked />
    </Stack>
  ),
};

export const LabelPlacements: Story = {
  render: () => (
    <Stack spacing={2} alignItems="flex-start">
      <MuiSwitch label="End (default)" labelPlacement="end" defaultChecked />
      <MuiSwitch label="Start" labelPlacement="start" defaultChecked />
      <MuiSwitch label="Top" labelPlacement="top" defaultChecked />
      <MuiSwitch label="Bottom" labelPlacement="bottom" defaultChecked />
    </Stack>
  ),
};
