import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MuiRadioGroup } from './MuiRadioGroup';
import { useState } from 'react';

const meta: Meta<typeof MuiRadioGroup> = {
  title: 'MUI Components/Inputs/RadioGroup',
  component: MuiRadioGroup,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI RadioGroup component for selecting one option from multiple choices.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    color: {
      control: { type: 'select' },
      options: ['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning'],
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const basicOptions = [
  { value: 'option1', label: 'Option 1' },
  { value: 'option2', label: 'Option 2' },
  { value: 'option3', label: 'Option 3' },
];

const genderOptions = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'other', label: 'Other' },
  { value: 'prefer-not-to-say', label: 'Prefer not to say' },
];

export const Default: Story = {
  args: {
    label: 'Choose an option',
    options: basicOptions,
    defaultValue: 'option1',
  },
};

export const Horizontal: Story = {
  args: {
    label: 'Gender',
    options: genderOptions,
    row: true,
  },
};

export const Controlled: Story = {
  render: (args) => {
    const [value, setValue] = useState('option1');
    return (
      <MuiRadioGroup
        {...args}
        value={value}
        onChange={(e) => setValue(e.target.value)}
      />
    );
  },
  args: {
    label: 'Controlled selection',
    options: basicOptions,
  },
};

export const WithDisabledOption: Story = {
  args: {
    label: 'Choose an option',
    options: [
      { value: 'option1', label: 'Option 1' },
      { value: 'option2', label: 'Option 2 (Disabled)', disabled: true },
      { value: 'option3', label: 'Option 3' },
    ],
  },
};

export const Colors: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '32px' }}>
      <MuiRadioGroup
        label="Primary"
        options={basicOptions}
        color="primary"
        defaultValue="option1"
      />
      <MuiRadioGroup
        label="Secondary"
        options={basicOptions}
        color="secondary"
        defaultValue="option1"
      />
      <MuiRadioGroup
        label="Success"
        options={basicOptions}
        color="success"
        defaultValue="option1"
      />
    </div>
  ),
};
