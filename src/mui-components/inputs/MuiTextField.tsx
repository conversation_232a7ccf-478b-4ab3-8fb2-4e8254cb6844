import React from 'react';
import { TextField, TextFieldProps } from '@mui/material';

export interface MuiTextFieldProps extends TextFieldProps {
  /**
   * The label for the text field
   */
  label?: string;
  /**
   * Placeholder text
   */
  placeholder?: string;
  /**
   * Helper text
   */
  helperText?: string;
  /**
   * Error state
   */
  error?: boolean;
  /**
   * Required field
   */
  required?: boolean;
  /**
   * Input variant
   */
  variant?: 'outlined' | 'filled' | 'standard';
  /**
   * Input type
   */
  type?: 'text' | 'password' | 'email' | 'number' | 'tel' | 'url' | 'search';
  /**
   * Whether the field is multiline
   */
  multiline?: boolean;
  /**
   * Number of rows for multiline
   */
  rows?: number;
  /**
   * Maximum number of rows for multiline
   */
  maxRows?: number;
}

/**
 * A Material-UI TextField component for text input
 */
export const MuiTextField: React.FC<MuiTextFieldProps> = ({
  label,
  placeholder,
  helperText,
  error = false,
  required = false,
  variant = 'outlined',
  type = 'text',
  multiline = false,
  rows,
  maxRows,
  ...props
}) => {
  return (
    <TextField
      label={label}
      placeholder={placeholder}
      helperText={helperText}
      error={error}
      required={required}
      variant={variant}
      type={type}
      multiline={multiline}
      rows={multiline ? rows : undefined}
      maxRows={multiline ? maxRows : undefined}
      {...props}
    />
  );
};
