import type { Meta, StoryObj } from '@storybook/react';
import { MuiButtonGroup } from './MuiButtonGroup';
import { useState } from 'react';

const meta: Meta<typeof MuiButtonGroup> = {
  title: 'MUI Components/Inputs/ButtonGroup',
  component: MuiButtonGroup,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI ButtonGroup component for grouping related buttons.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['text', 'outlined', 'contained'],
    },
    color: {
      control: { type: 'select' },
      options: ['inherit', 'primary', 'secondary', 'success', 'error', 'info', 'warning'],
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large'],
    },
    orientation: {
      control: { type: 'select' },
      options: ['horizontal', 'vertical'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    buttons: ['One', 'Two', 'Three'],
  },
};

export const Vertical: Story = {
  args: {
    buttons: ['One', 'Two', 'Three'],
    orientation: 'vertical',
  },
};

export const Contained: Story = {
  args: {
    buttons: ['One', 'Two', 'Three'],
    variant: 'contained',
  },
};

export const WithSelection: Story = {
  render: (args) => {
    const [selected, setSelected] = useState(0);
    return (
      <MuiButtonGroup
        {...args}
        selectedIndex={selected}
        onButtonClick={(index) => setSelected(index)}
      />
    );
  },
  args: {
    buttons: ['Left', 'Center', 'Right'],
  },
};

export const Disabled: Story = {
  args: {
    buttons: ['One', 'Two', 'Three'],
    disabled: true,
  },
};
