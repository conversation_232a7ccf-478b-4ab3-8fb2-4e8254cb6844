import React from 'react';
import { Rating, RatingProps, Typography, Box } from '@mui/material';
import StarIcon from '@mui/icons-material/Star';

export interface MuiRatingProps extends RatingProps {
  /**
   * Label for the rating
   */
  label?: string;
  /**
   * Show the current value as text
   */
  showValue?: boolean;
  /**
   * Custom labels for each rating value
   */
  labels?: { [index: string]: string };
}

/**
 * A Material-UI Rating component with optional label and value display
 */
export const MuiRating: React.FC<MuiRatingProps> = ({
  label,
  showValue = false,
  labels,
  value,
  ...props
}) => {
  const getLabelText = (value: number) => {
    if (labels && labels[value]) {
      return labels[value];
    }
    return `${value} Star${value !== 1 ? 's' : ''}`;
  };

  return (
    <Box>
      {label && (
        <Typography component="legend" gutterBottom>
          {label}
        </Typography>
      )}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Rating
          value={value}
          getLabelText={getLabelText}
          emptyIcon={<StarIcon style={{ opacity: 0.55 }} fontSize="inherit" />}
          {...props}
        />
        {showValue && value !== null && (
          <Typography variant="body2" color="text.secondary">
            {labels && labels[value as number] ? labels[value as number] : value}
          </Typography>
        )}
      </Box>
    </Box>
  );
};
