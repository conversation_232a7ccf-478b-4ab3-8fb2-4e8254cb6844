import React from 'react';
import {
  ToggleButton,
  ToggleButtonGroup,
  ToggleButtonProps,
  ToggleButtonGroupProps,
} from '@mui/material';

export interface ToggleOption {
  value: string | number;
  label: string;
  icon?: React.ReactNode;
  disabled?: boolean;
}

export interface MuiToggleButtonProps extends Omit<ToggleButtonProps, 'value'> {
  /**
   * The value of this toggle button
   */
  value: string | number;
  /**
   * The label text
   */
  label?: string;
  /**
   * Optional icon
   */
  icon?: React.ReactNode;
}

export interface MuiToggleButtonGroupProps extends Omit<ToggleButtonGroupProps, 'children'> {
  /**
   * Array of toggle button options
   */
  options: ToggleOption[];
  /**
   * Whether multiple buttons can be selected
   */
  multiple?: boolean;
  /**
   * Current selected value(s)
   */
  value?: string | number | (string | number)[];
  /**
   * Callback when selection changes
   */
  onChange?: (
    event: React.MouseEvent<HTMLElement>,
    newValue: string | number | (string | number)[] | null
  ) => void;
}

/**
 * A single Material-UI ToggleButton component
 */
export const MuiToggleButton: React.FC<MuiToggleButtonProps> = ({
  value,
  label,
  icon,
  children,
  ...props
}) => {
  return (
    <ToggleButton value={value} {...props}>
      {icon && icon}
      {label || children}
    </ToggleButton>
  );
};

/**
 * A Material-UI ToggleButtonGroup component for grouped toggle buttons
 */
export const MuiToggleButtonGroup: React.FC<MuiToggleButtonGroupProps> = ({
  options,
  multiple = false,
  value,
  onChange,
  orientation = 'horizontal',
  color = 'primary',
  size = 'medium',
  ...props
}) => {
  return (
    <ToggleButtonGroup
      value={value}
      onChange={onChange}
      exclusive={!multiple}
      orientation={orientation}
      color={color}
      size={size}
      {...props}
    >
      {options.map((option) => (
        <ToggleButton
          key={option.value}
          value={option.value}
          disabled={option.disabled}
        >
          {option.icon && option.icon}
          {option.label}
        </ToggleButton>
      ))}
    </ToggleButtonGroup>
  );
};
