import type { Meta, StoryObj } from '@storybook/react';
import { MuiSlider } from './MuiSlider';
import { useState } from 'react';
import { Stack } from '@mui/material';

const meta: Meta<typeof MuiSlider> = {
  title: 'MUI Components/Inputs/Slider',
  component: MuiSlider,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Slider component for selecting values from a range.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    color: {
      control: { type: 'select' },
      options: ['primary', 'secondary'],
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium'],
    },
    orientation: {
      control: { type: 'select' },
      options: ['horizontal', 'vertical'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    label: 'Volume',
    defaultValue: 30,
    showValue: true,
  },
};

export const Controlled: Story = {
  render: (args) => {
    const [value, setValue] = useState(50);
    return (
      <MuiSlider
        {...args}
        value={value}
        onChange={(event, newValue) => setValue(newValue as number)}
      />
    );
  },
  args: {
    label: 'Controlled Slider',
    showValue: true,
    min: 0,
    max: 100,
  },
};

export const WithMarks: Story = {
  args: {
    label: 'Temperature',
    defaultValue: 20,
    showValue: true,
    showMarks: true,
    min: 0,
    max: 100,
    step: 10,
    valueLabelDisplay: 'auto',
  },
};

export const CustomMarks: Story = {
  args: {
    label: 'Size',
    defaultValue: 2,
    showValue: true,
    marks: [
      { value: 1, label: 'Small' },
      { value: 2, label: 'Medium' },
      { value: 3, label: 'Large' },
    ],
    min: 1,
    max: 3,
    step: 1,
    valueLabelDisplay: 'off',
  },
};

export const Range: Story = {
  render: (args) => {
    const [value, setValue] = useState([20, 80]);
    return (
      <MuiSlider
        {...args}
        value={value}
        onChange={(event, newValue) => setValue(newValue as number[])}
      />
    );
  },
  args: {
    label: 'Price Range',
    showValue: true,
    min: 0,
    max: 100,
    valueLabelFormat: (value) => `$${value}`,
  },
};

export const Vertical: Story = {
  render: (args) => {
    const [value, setValue] = useState(30);
    return (
      <div style={{ height: 300, display: 'flex', justifyContent: 'center' }}>
        <MuiSlider
          {...args}
          value={value}
          onChange={(event, newValue) => setValue(newValue as number)}
        />
      </div>
    );
  },
  args: {
    label: 'Vertical Slider',
    orientation: 'vertical',
    showValue: true,
  },
};

export const Disabled: Story = {
  args: {
    label: 'Disabled Slider',
    defaultValue: 50,
    disabled: true,
    showValue: true,
  },
};

export const Colors: Story = {
  render: () => (
    <Stack spacing={4} sx={{ width: 300 }}>
      <MuiSlider
        label="Primary (default)"
        defaultValue={30}
        color="primary"
        showValue
      />
      <MuiSlider
        label="Secondary"
        defaultValue={60}
        color="secondary"
        showValue
      />
    </Stack>
  ),
};
