import React from 'react';
import { Slider, SliderProps, Typography, Box } from '@mui/material';

export interface MuiSliderProps extends SliderProps {
  /**
   * Label for the slider
   */
  label?: string;
  /**
   * Whether to show the current value
   */
  showValue?: boolean;
  /**
   * Custom value label format function
   */
  valueLabelFormat?: (value: number) => string;
  /**
   * Whether to show marks
   */
  showMarks?: boolean;
  /**
   * Custom marks array
   */
  marks?: Array<{ value: number; label?: string }>;
}

/**
 * A Material-UI Slider component for selecting values from a range
 */
export const MuiSlider: React.FC<MuiSliderProps> = ({
  label,
  showValue = false,
  valueLabelFormat,
  showMarks = false,
  marks,
  value,
  ...props
}) => {
  const formatValue = (val: number) => {
    if (valueLabelFormat) {
      return valueLabelFormat(val);
    }
    return val.toString();
  };

  return (
    <Box sx={{ width: '100%' }}>
      {label && (
        <Typography gutterBottom>
          {label}
          {showValue && value !== undefined && (
            <span style={{ marginLeft: 8, fontWeight: 'bold' }}>
              {Array.isArray(value) 
                ? `[${value.map(formatValue).join(', ')}]`
                : formatValue(value as number)
              }
            </span>
          )}
        </Typography>
      )}
      <Slider
        value={value}
        valueLabelFormat={valueLabelFormat}
        marks={showMarks ? (marks || true) : false}
        {...props}
      />
    </Box>
  );
};
