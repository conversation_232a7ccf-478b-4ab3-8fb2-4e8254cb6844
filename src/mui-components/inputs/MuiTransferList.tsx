import React, { useState } from 'react';
import {
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Checkbox,
  Button,
  Paper,
  Typography,
} from '@mui/material';

export interface TransferListItem {
  id: string | number;
  label: string;
  disabled?: boolean;
}

export interface MuiTransferListProps {
  /**
   * Available items on the left side
   */
  leftItems: TransferListItem[];
  /**
   * Selected items on the right side
   */
  rightItems: TransferListItem[];
  /**
   * Callback when items are transferred
   */
  onChange: (leftItems: TransferListItem[], rightItems: TransferListItem[]) => void;
  /**
   * Title for the left list
   */
  leftTitle?: string;
  /**
   * Title for the right list
   */
  rightTitle?: string;
  /**
   * Whether the transfer list is disabled
   */
  disabled?: boolean;
}

function not(a: TransferListItem[], b: TransferListItem[]) {
  return a.filter((value) => b.findIndex(item => item.id === value.id) === -1);
}

function intersection(a: TransferListItem[], b: TransferListItem[]) {
  return a.filter((value) => b.findIndex(item => item.id === value.id) !== -1);
}

/**
 * A Material-UI Transfer List component for moving items between two lists
 */
export const MuiTransferList: React.FC<MuiTransferListProps> = ({
  leftItems,
  rightItems,
  onChange,
  leftTitle = 'Available',
  rightTitle = 'Selected',
  disabled = false,
}) => {
  const [checked, setChecked] = useState<TransferListItem[]>([]);

  const leftChecked = intersection(checked, leftItems);
  const rightChecked = intersection(checked, rightItems);

  const handleToggle = (value: TransferListItem) => () => {
    if (disabled || value.disabled) return;
    
    const currentIndex = checked.findIndex(item => item.id === value.id);
    const newChecked = [...checked];

    if (currentIndex === -1) {
      newChecked.push(value);
    } else {
      newChecked.splice(currentIndex, 1);
    }

    setChecked(newChecked);
  };

  const handleAllRight = () => {
    const newRightItems = [...rightItems, ...leftItems];
    const newLeftItems: TransferListItem[] = [];
    onChange(newLeftItems, newRightItems);
    setChecked([]);
  };

  const handleCheckedRight = () => {
    const newRightItems = [...rightItems, ...leftChecked];
    const newLeftItems = not(leftItems, leftChecked);
    onChange(newLeftItems, newRightItems);
    setChecked(not(checked, leftChecked));
  };

  const handleCheckedLeft = () => {
    const newLeftItems = [...leftItems, ...rightChecked];
    const newRightItems = not(rightItems, rightChecked);
    onChange(newLeftItems, newRightItems);
    setChecked(not(checked, rightChecked));
  };

  const handleAllLeft = () => {
    const newLeftItems = [...leftItems, ...rightItems];
    const newRightItems: TransferListItem[] = [];
    onChange(newLeftItems, newRightItems);
    setChecked([]);
  };

  const customList = (title: string, items: TransferListItem[]) => (
    <Paper sx={{ width: 200, height: 230, overflow: 'auto' }}>
      <Typography variant="h6" sx={{ p: 1, textAlign: 'center' }}>
        {title}
      </Typography>
      <List dense component="div" role="list">
        {items.map((item) => {
          const labelId = `transfer-list-item-${item.id}-label`;
          const isChecked = checked.findIndex(checkedItem => checkedItem.id === item.id) !== -1;

          return (
            <ListItem
              key={item.id}
              role="listitem"
              button
              onClick={handleToggle(item)}
              disabled={disabled || item.disabled}
            >
              <ListItemIcon>
                <Checkbox
                  checked={isChecked}
                  tabIndex={-1}
                  disableRipple
                  inputProps={{
                    'aria-labelledby': labelId,
                  }}
                  disabled={disabled || item.disabled}
                />
              </ListItemIcon>
              <ListItemText id={labelId} primary={item.label} />
            </ListItem>
          );
        })}
      </List>
    </Paper>
  );

  return (
    <Grid container spacing={2} justifyContent="center" alignItems="center">
      <Grid item>{customList(leftTitle, leftItems)}</Grid>
      <Grid item>
        <Grid container direction="column" alignItems="center">
          <Button
            sx={{ my: 0.5 }}
            variant="outlined"
            size="small"
            onClick={handleAllRight}
            disabled={disabled || leftItems.length === 0}
            aria-label="move all right"
          >
            ≫
          </Button>
          <Button
            sx={{ my: 0.5 }}
            variant="outlined"
            size="small"
            onClick={handleCheckedRight}
            disabled={disabled || leftChecked.length === 0}
            aria-label="move selected right"
          >
            &gt;
          </Button>
          <Button
            sx={{ my: 0.5 }}
            variant="outlined"
            size="small"
            onClick={handleCheckedLeft}
            disabled={disabled || rightChecked.length === 0}
            aria-label="move selected left"
          >
            &lt;
          </Button>
          <Button
            sx={{ my: 0.5 }}
            variant="outlined"
            size="small"
            onClick={handleAllLeft}
            disabled={disabled || rightItems.length === 0}
            aria-label="move all left"
          >
            ≪
          </Button>
        </Grid>
      </Grid>
      <Grid item>{customList(rightTitle, rightItems)}</Grid>
    </Grid>
  );
};
