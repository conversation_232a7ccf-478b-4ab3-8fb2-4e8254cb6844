import React from 'react';
import { Checkbox, FormControlLabel, FormGroup, CheckboxProps } from '@mui/material';

export interface MuiCheckboxProps extends CheckboxProps {
  /**
   * The label for the checkbox
   */
  label?: string;
  /**
   * Whether to show the label
   */
  showLabel?: boolean;
  /**
   * Label placement
   */
  labelPlacement?: 'end' | 'start' | 'top' | 'bottom';
}

/**
 * A Material-UI Checkbox component with optional label
 */
export const MuiCheckbox: React.FC<MuiCheckboxProps> = ({
  label,
  showLabel = true,
  labelPlacement = 'end',
  ...props
}) => {
  if (!showLabel || !label) {
    return <Checkbox {...props} />;
  }

  return (
    <FormGroup>
      <FormControlLabel
        control={<Checkbox {...props} />}
        label={label}
        labelPlacement={labelPlacement}
      />
    </FormGroup>
  );
};
