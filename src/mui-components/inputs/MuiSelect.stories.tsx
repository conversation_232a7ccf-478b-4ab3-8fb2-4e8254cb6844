import type { Meta, StoryObj } from '@storybook/react';
import { MuiSelect } from './MuiSelect';
import { useState } from 'react';
import { Stack } from '@mui/material';

const meta: Meta<typeof MuiSelect> = {
  title: 'MUI Components/Inputs/Select',
  component: MuiSelect,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Select component for choosing from a list of options.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['outlined', 'filled', 'standard'],
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const countryOptions = [
  { value: 'us', label: 'United States' },
  { value: 'ca', label: 'Canada' },
  { value: 'mx', label: 'Mexico' },
  { value: 'uk', label: 'United Kingdom' },
  { value: 'de', label: 'Germany' },
  { value: 'fr', label: 'France' },
  { value: 'jp', label: 'Japan' },
  { value: 'au', label: 'Australia' },
];

const priorityOptions = [
  { value: 'low', label: 'Low' },
  { value: 'medium', label: 'Medium' },
  { value: 'high', label: 'High' },
  { value: 'urgent', label: 'Urgent', disabled: true },
];

export const Default: Story = {
  args: {
    label: 'Select Country',
    options: countryOptions,
    placeholder: 'Choose a country',
    sx: { minWidth: 200 },
  },
};

export const Controlled: Story = {
  render: (args) => {
    const [value, setValue] = useState('');
    return (
      <MuiSelect
        {...args}
        value={value}
        onChange={(event) => setValue(event.target.value as string)}
      />
    );
  },
  args: {
    label: 'Controlled Select',
    options: countryOptions,
    helperText: 'This select is controlled by React state',
    sx: { minWidth: 200 },
  },
};

export const WithError: Story = {
  args: {
    label: 'Priority Level',
    options: priorityOptions,
    error: true,
    helperText: 'Please select a priority level',
    sx: { minWidth: 200 },
  },
};

export const Required: Story = {
  args: {
    label: 'Required Field',
    options: countryOptions,
    required: true,
    helperText: 'This field is required',
    sx: { minWidth: 200 },
  },
};

export const WithDisabledOption: Story = {
  args: {
    label: 'Priority',
    options: priorityOptions,
    helperText: 'Some options may be disabled',
    sx: { minWidth: 200 },
  },
};

export const Multiple: Story = {
  render: (args) => {
    const [value, setValue] = useState<string[]>([]);
    return (
      <MuiSelect
        {...args}
        value={value}
        onChange={(event) => setValue(event.target.value as string[])}
        multiple
      />
    );
  },
  args: {
    label: 'Multiple Selection',
    options: countryOptions,
    helperText: 'You can select multiple countries',
    sx: { minWidth: 200 },
  },
};

export const Variants: Story = {
  render: () => (
    <Stack spacing={3} sx={{ width: 300 }}>
      <MuiSelect
        label="Outlined (default)"
        variant="outlined"
        options={countryOptions}
        placeholder="Select country"
      />
      <MuiSelect
        label="Filled"
        variant="filled"
        options={countryOptions}
        placeholder="Select country"
      />
      <MuiSelect
        label="Standard"
        variant="standard"
        options={countryOptions}
        placeholder="Select country"
      />
    </Stack>
  ),
};

export const Disabled: Story = {
  args: {
    label: 'Disabled Select',
    options: countryOptions,
    disabled: true,
    value: 'us',
    helperText: 'This select is disabled',
    sx: { minWidth: 200 },
  },
};
