import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { Mui<PERSON>og<PERSON>Button, MuiToggleButtonGroup } from './MuiToggleButton';
import { useState } from 'react';
import { Stack } from '@mui/material';
import FormatBoldIcon from '@mui/icons-material/FormatBold';
import FormatItalicIcon from '@mui/icons-material/FormatItalic';
import FormatUnderlinedIcon from '@mui/icons-material/FormatUnderlined';
import FormatAlignLeftIcon from '@mui/icons-material/FormatAlignLeft';
import FormatAlignCenterIcon from '@mui/icons-material/FormatAlignCenter';
import FormatAlignRightIcon from '@mui/icons-material/FormatAlignRight';
import ViewListIcon from '@mui/icons-material/ViewList';
import ViewModuleIcon from '@mui/icons-material/ViewModule';
import ViewQuiltIcon from '@mui/icons-material/ViewQuilt';

const meta: Meta<typeof MuiToggleButtonGroup> = {
  title: 'MUI Components/Inputs/ToggleButton',
  component: MuiToggleButtonGroup,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Material-UI ToggleButton components for selecting from multiple options.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    color: {
      control: { type: 'select' },
      options: ['standard', 'primary', 'secondary', 'error', 'info', 'success', 'warning'],
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large'],
    },
    orientation: {
      control: { type: 'select' },
      options: ['horizontal', 'vertical'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const textFormatOptions = [
  { value: 'bold', label: 'Bold', icon: <FormatBoldIcon /> },
  { value: 'italic', label: 'Italic', icon: <FormatItalicIcon /> },
  { value: 'underlined', label: 'Underlined', icon: <FormatUnderlinedIcon /> },
];

const alignmentOptions = [
  { value: 'left', label: '', icon: <FormatAlignLeftIcon /> },
  { value: 'center', label: '', icon: <FormatAlignCenterIcon /> },
  { value: 'right', label: '', icon: <FormatAlignRightIcon /> },
];

const viewOptions = [
  { value: 'list', label: '', icon: <ViewListIcon /> },
  { value: 'module', label: '', icon: <ViewModuleIcon /> },
  { value: 'quilt', label: '', icon: <ViewQuiltIcon /> },
];

const simpleOptions = [
  { value: 'option1', label: 'Option 1' },
  { value: 'option2', label: 'Option 2' },
  { value: 'option3', label: 'Option 3' },
];

export const Default: Story = {
  args: {
    options: simpleOptions,
  },
};

export const Controlled: Story = {
  render: (args) => {
    const [value, setValue] = useState<string>('option1');
    return (
      <MuiToggleButtonGroup
        {...args}
        value={value}
        onChange={(event, newValue) => {
          if (newValue !== null) {
            setValue(newValue as string);
          }
        }}
      />
    );
  },
  args: {
    options: simpleOptions,
  },
};

export const MultipleSelection: Story = {
  render: (args) => {
    const [formats, setFormats] = useState<string[]>(['bold']);
    return (
      <MuiToggleButtonGroup
        {...args}
        value={formats}
        onChange={(event, newFormats) => setFormats(newFormats as string[])}
        multiple
      />
    );
  },
  args: {
    options: textFormatOptions,
  },
};

export const IconOnly: Story = {
  render: (args) => {
    const [alignment, setAlignment] = useState<string>('left');
    return (
      <MuiToggleButtonGroup
        {...args}
        value={alignment}
        onChange={(event, newAlignment) => {
          if (newAlignment !== null) {
            setAlignment(newAlignment as string);
          }
        }}
      />
    );
  },
  args: {
    options: alignmentOptions,
  },
};

export const Vertical: Story = {
  render: (args) => {
    const [view, setView] = useState<string>('list');
    return (
      <MuiToggleButtonGroup
        {...args}
        value={view}
        onChange={(event, newView) => {
          if (newView !== null) {
            setView(newView as string);
          }
        }}
      />
    );
  },
  args: {
    options: viewOptions,
    orientation: 'vertical',
  },
};

export const Colors: Story = {
  render: () => (
    <Stack spacing={3}>
      <MuiToggleButtonGroup
        options={simpleOptions}
        color="primary"
        value="option1"
      />
      <MuiToggleButtonGroup
        options={simpleOptions}
        color="secondary"
        value="option1"
      />
      <MuiToggleButtonGroup
        options={simpleOptions}
        color="success"
        value="option1"
      />
      <MuiToggleButtonGroup
        options={simpleOptions}
        color="error"
        value="option1"
      />
    </Stack>
  ),
};

export const Sizes: Story = {
  render: () => (
    <Stack spacing={3}>
      <MuiToggleButtonGroup
        options={simpleOptions}
        size="small"
        value="option1"
      />
      <MuiToggleButtonGroup
        options={simpleOptions}
        size="medium"
        value="option1"
      />
      <MuiToggleButtonGroup
        options={simpleOptions}
        size="large"
        value="option1"
      />
    </Stack>
  ),
};

export const WithDisabledOption: Story = {
  args: {
    options: [
      { value: 'option1', label: 'Option 1' },
      { value: 'option2', label: 'Option 2 (Disabled)', disabled: true },
      { value: 'option3', label: 'Option 3' },
    ],
  },
};
