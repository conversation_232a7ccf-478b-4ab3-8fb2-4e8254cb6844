import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MuiAutocomplete } from './MuiAutocomplete';

const meta: Meta<typeof MuiAutocomplete> = {
  title: 'MUI Components/Inputs/Autocomplete',
  component: MuiAutocomplete,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Autocomplete component for selecting from a list of options.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['outlined', 'filled', 'standard'],
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const topMovies = [
  { label: 'The Shawshank Redemption', year: 1994 },
  { label: 'The Godfather', year: 1972 },
  { label: 'The Godfather: Part II', year: 1974 },
  { label: 'The Dark Knight', year: 2008 },
  { label: '12 Angry Men', year: 1957 },
  { label: "Schindler's List", year: 1993 },
  { label: 'Pulp Fiction', year: 1994 },
];

export const Default: Story = {
  args: {
    options: topMovies,
    label: 'Choose a movie',
    placeholder: 'Start typing...',
    sx: { width: 300 },
  },
};

export const Multiple: Story = {
  args: {
    options: topMovies,
    label: 'Choose movies',
    placeholder: 'Select multiple movies',
    multiple: true,
    sx: { width: 300 },
  },
};

export const WithError: Story = {
  args: {
    options: topMovies,
    label: 'Choose a movie',
    error: true,
    helperText: 'Please select a movie',
    sx: { width: 300 },
  },
};

export const Disabled: Story = {
  args: {
    options: topMovies,
    label: 'Choose a movie',
    disabled: true,
    sx: { width: 300 },
  },
};

export const FreeSolo: Story = {
  args: {
    options: topMovies.map(movie => movie.label),
    label: 'Free solo input',
    freeSolo: true,
    sx: { width: 300 },
  },
};
