import React from 'react';
import { ButtonGroup, Button, ButtonGroupProps } from '@mui/material';

export interface MuiButtonGroupProps extends ButtonGroupProps {
  /**
   * Array of button labels
   */
  buttons: string[];
  /**
   * Callback when a button is clicked
   */
  onButtonClick?: (index: number, label: string) => void;
  /**
   * Currently selected button index
   */
  selectedIndex?: number;
}

/**
 * A Material-UI ButtonGroup component with customizable buttons
 */
export const MuiButtonGroup: React.FC<MuiButtonGroupProps> = ({
  buttons,
  onButtonClick,
  selectedIndex,
  variant = 'outlined',
  color = 'primary',
  size = 'medium',
  ...props
}) => {
  return (
    <ButtonGroup
      variant={variant}
      color={color}
      size={size}
      {...props}
    >
      {buttons.map((label, index) => (
        <Button
          key={index}
          onClick={() => onButtonClick?.(index, label)}
          variant={selectedIndex === index ? 'contained' : variant}
        >
          {label}
        </Button>
      ))}
    </ButtonGroup>
  );
};
