import React from 'react';
import { Autocomplete, TextField, AutocompleteProps } from '@mui/material';

export interface MuiAutocompleteProps extends Omit<AutocompleteProps<any, boolean, boolean, boolean>, 'renderInput'> {
  /**
   * The label for the input field
   */
  label?: string;
  /**
   * Placeholder text
   */
  placeholder?: string;
  /**
   * Helper text
   */
  helperText?: string;
  /**
   * Error state
   */
  error?: boolean;
  /**
   * Required field
   */
  required?: boolean;
  /**
   * Input variant
   */
  variant?: 'outlined' | 'filled' | 'standard';
}

/**
 * A Material-UI Autocomplete component with customizable options
 */
export const MuiAutocomplete: React.FC<MuiAutocompleteProps> = ({
  label = 'Select option',
  placeholder,
  helperText,
  error = false,
  required = false,
  variant = 'outlined',
  ...props
}) => {
  return (
    <Autocomplete
      {...props}
      renderInput={(params) => (
        <TextField
          {...params}
          label={label}
          placeholder={placeholder}
          helperText={helperText}
          error={error}
          required={required}
          variant={variant}
        />
      )}
    />
  );
};
