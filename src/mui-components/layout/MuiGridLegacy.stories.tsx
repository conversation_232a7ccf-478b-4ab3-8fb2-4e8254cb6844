import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { MuiGridLegacy } from './MuiGridLegacy';
import { Paper, Typography, Box } from '@mui/material';

const meta: Meta<typeof MuiGridLegacy> = {
  title: 'MUI Components/Layout/GridLegacy',
  component: MuiGridLegacy,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'A Material-UI Grid (v1) component for responsive layouts using the legacy Grid system.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    direction: {
      control: { type: 'select' },
      options: ['row', 'row-reverse', 'column', 'column-reverse'],
    },
    justifyContent: {
      control: { type: 'select' },
      options: ['flex-start', 'center', 'flex-end', 'space-between', 'space-around', 'space-evenly'],
    },
    alignItems: {
      control: { type: 'select' },
      options: ['flex-start', 'center', 'flex-end', 'stretch', 'baseline'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof MuiGridLegacy>;

const GridItem = ({ children, ...props }: any) => (
  <MuiGridLegacy item {...props}>
    <Paper sx={{ p: 2, textAlign: 'center', backgroundColor: 'primary.light', color: 'white' }}>
      {children}
    </Paper>
  </MuiGridLegacy>
);

export const BasicGrid: Story = {
  render: () => (
    <MuiGridLegacy container spacing={2}>
      <GridItem xs={12}>
        <Typography>xs=12</Typography>
      </GridItem>
      <GridItem xs={6}>
        <Typography>xs=6</Typography>
      </GridItem>
      <GridItem xs={6}>
        <Typography>xs=6</Typography>
      </GridItem>
      <GridItem xs={3}>
        <Typography>xs=3</Typography>
      </GridItem>
      <GridItem xs={3}>
        <Typography>xs=3</Typography>
      </GridItem>
      <GridItem xs={3}>
        <Typography>xs=3</Typography>
      </GridItem>
      <GridItem xs={3}>
        <Typography>xs=3</Typography>
      </GridItem>
    </MuiGridLegacy>
  ),
};

export const ResponsiveGrid: Story = {
  render: () => (
    <MuiGridLegacy container spacing={2}>
      <GridItem xs={12} sm={6} md={4} lg={3}>
        <Typography variant="body2">
          xs=12 sm=6 md=4 lg=3
        </Typography>
      </GridItem>
      <GridItem xs={12} sm={6} md={4} lg={3}>
        <Typography variant="body2">
          xs=12 sm=6 md=4 lg=3
        </Typography>
      </GridItem>
      <GridItem xs={12} sm={6} md={4} lg={3}>
        <Typography variant="body2">
          xs=12 sm=6 md=4 lg=3
        </Typography>
      </GridItem>
      <GridItem xs={12} sm={6} md={4} lg={3}>
        <Typography variant="body2">
          xs=12 sm=6 md=4 lg=3
        </Typography>
      </GridItem>
    </MuiGridLegacy>
  ),
};

export const NestedGrid: Story = {
  render: () => (
    <MuiGridLegacy container spacing={2}>
      <MuiGridLegacy item xs={12} md={8}>
        <Paper sx={{ p: 2, backgroundColor: 'secondary.light' }}>
          <Typography variant="h6" gutterBottom>
            Main Content (xs=12 md=8)
          </Typography>
          <MuiGridLegacy container spacing={1}>
            <GridItem xs={6}>
              <Typography variant="body2">Nested xs=6</Typography>
            </GridItem>
            <GridItem xs={6}>
              <Typography variant="body2">Nested xs=6</Typography>
            </GridItem>
          </MuiGridLegacy>
        </Paper>
      </MuiGridLegacy>
      <MuiGridLegacy item xs={12} md={4}>
        <Paper sx={{ p: 2, backgroundColor: 'info.light', color: 'white' }}>
          <Typography variant="h6">
            Sidebar (xs=12 md=4)
          </Typography>
        </Paper>
      </MuiGridLegacy>
    </MuiGridLegacy>
  ),
};

export const AutoSizing: Story = {
  render: () => (
    <MuiGridLegacy container spacing={2}>
      <GridItem xs="auto">
        <Typography>Auto width</Typography>
      </GridItem>
      <GridItem xs={6}>
        <Typography>xs=6</Typography>
      </GridItem>
      <GridItem xs="auto">
        <Typography>Auto width</Typography>
      </GridItem>
    </MuiGridLegacy>
  ),
};

export const Justification: Story = {
  render: () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Justify Content: flex-start
      </Typography>
      <MuiGridLegacy container spacing={1} justifyContent="flex-start" sx={{ mb: 2 }}>
        <GridItem xs={2}><Typography>1</Typography></GridItem>
        <GridItem xs={2}><Typography>2</Typography></GridItem>
      </MuiGridLegacy>
      
      <Typography variant="h6" gutterBottom>
        Justify Content: center
      </Typography>
      <MuiGridLegacy container spacing={1} justifyContent="center" sx={{ mb: 2 }}>
        <GridItem xs={2}><Typography>1</Typography></GridItem>
        <GridItem xs={2}><Typography>2</Typography></GridItem>
      </MuiGridLegacy>
      
      <Typography variant="h6" gutterBottom>
        Justify Content: space-between
      </Typography>
      <MuiGridLegacy container spacing={1} justifyContent="space-between">
        <GridItem xs={2}><Typography>1</Typography></GridItem>
        <GridItem xs={2}><Typography>2</Typography></GridItem>
      </MuiGridLegacy>
    </Box>
  ),
};

export const Direction: Story = {
  render: () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Direction: row (default)
      </Typography>
      <MuiGridLegacy container spacing={1} direction="row" sx={{ mb: 3 }}>
        <GridItem xs={3}><Typography>1</Typography></GridItem>
        <GridItem xs={3}><Typography>2</Typography></GridItem>
        <GridItem xs={3}><Typography>3</Typography></GridItem>
      </MuiGridLegacy>
      
      <Typography variant="h6" gutterBottom>
        Direction: column
      </Typography>
      <MuiGridLegacy container spacing={1} direction="column" sx={{ height: 200 }}>
        <GridItem xs={3}><Typography>1</Typography></GridItem>
        <GridItem xs={3}><Typography>2</Typography></GridItem>
        <GridItem xs={3}><Typography>3</Typography></GridItem>
      </MuiGridLegacy>
    </Box>
  ),
};

export const DifferentSpacing: Story = {
  render: () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Spacing = 0
      </Typography>
      <MuiGridLegacy container spacing={0} sx={{ mb: 4 }}>
        <GridItem xs={4}><Typography>Item 1</Typography></GridItem>
        <GridItem xs={4}><Typography>Item 2</Typography></GridItem>
        <GridItem xs={4}><Typography>Item 3</Typography></GridItem>
      </MuiGridLegacy>
      
      <Typography variant="h6" gutterBottom>
        Spacing = 2
      </Typography>
      <MuiGridLegacy container spacing={2} sx={{ mb: 4 }}>
        <GridItem xs={4}><Typography>Item 1</Typography></GridItem>
        <GridItem xs={4}><Typography>Item 2</Typography></GridItem>
        <GridItem xs={4}><Typography>Item 3</Typography></GridItem>
      </MuiGridLegacy>
      
      <Typography variant="h6" gutterBottom>
        Spacing = 4
      </Typography>
      <MuiGridLegacy container spacing={4}>
        <GridItem xs={4}><Typography>Item 1</Typography></GridItem>
        <GridItem xs={4}><Typography>Item 2</Typography></GridItem>
        <GridItem xs={4}><Typography>Item 3</Typography></GridItem>
      </MuiGridLegacy>
    </Box>
  ),
};
