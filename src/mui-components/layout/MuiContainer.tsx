import React from 'react';
import { Container, ContainerProps } from '@mui/material';

export interface MuiContainerProps extends ContainerProps {
  /**
   * The content of the container
   */
  children: React.ReactNode;
  /**
   * The maximum width of the container
   */
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false;
  /**
   * Whether the container should be fixed width
   */
  fixed?: boolean;
  /**
   * Whether to disable gutters
   */
  disableGutters?: boolean;
  /**
   * Custom padding
   */
  padding?: number | string;
  /**
   * Custom margin
   */
  margin?: number | string;
  /**
   * Whether to center the container
   */
  centered?: boolean;
}

/**
 * A Material-UI Container component for centering content horizontally
 */
export const MuiContainer: React.FC<MuiContainerProps> = ({
  children,
  maxWidth = 'lg',
  fixed = false,
  disableGutters = false,
  padding,
  margin,
  centered = false,
  sx,
  ...props
}) => {
  const customSx = {
    ...(padding && { p: padding }),
    ...(margin && { m: margin }),
    ...(centered && {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
    }),
    ...sx,
  };

  return (
    <Container
      maxWidth={maxWidth}
      fixed={fixed}
      disableGutters={disableGutters}
      sx={customSx}
      {...props}
    >
      {children}
    </Container>
  );
};
