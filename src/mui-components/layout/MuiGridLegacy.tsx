import React from 'react';
import { Grid, GridProps } from '@mui/material';

export interface MuiGridLegacyProps extends GridProps {
  /**
   * The content of the grid
   */
  children: React.ReactNode;
  /**
   * Whether this is a container grid
   */
  container?: boolean;
  /**
   * Whether this is an item grid
   */
  item?: boolean;
  /**
   * The spacing between grid items
   */
  spacing?: number;
  /**
   * Grid size for extra small screens
   */
  xs?: boolean | 'auto' | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  /**
   * Grid size for small screens
   */
  sm?: boolean | 'auto' | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  /**
   * Grid size for medium screens
   */
  md?: boolean | 'auto' | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  /**
   * Grid size for large screens
   */
  lg?: boolean | 'auto' | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  /**
   * Grid size for extra large screens
   */
  xl?: boolean | 'auto' | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  /**
   * Justify content
   */
  justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  /**
   * Align items
   */
  alignItems?: 'flex-start' | 'center' | 'flex-end' | 'stretch' | 'baseline';
  /**
   * Direction of the grid
   */
  direction?: 'row' | 'row-reverse' | 'column' | 'column-reverse';
  /**
   * Wrap behavior
   */
  wrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
}

/**
 * A Material-UI Grid (v1) component for responsive layouts using the legacy Grid system
 */
export const MuiGridLegacy: React.FC<MuiGridLegacyProps> = ({
  children,
  container = false,
  item = false,
  spacing = 0,
  xs,
  sm,
  md,
  lg,
  xl,
  justifyContent,
  alignItems,
  direction,
  wrap,
  ...props
}) => {
  return (
    <Grid
      container={container}
      item={item}
      spacing={container ? spacing : undefined}
      xs={xs}
      sm={sm}
      md={md}
      lg={lg}
      xl={xl}
      justifyContent={justifyContent}
      alignItems={alignItems}
      direction={direction}
      wrap={wrap}
      {...props}
    >
      {children}
    </Grid>
  );
};
