import React from 'react';
import { Grid, GridProps } from '@mui/material';

export interface MuiGridProps extends GridProps {
  /**
   * The content of the grid
   */
  children: React.ReactNode;
  /**
   * The spacing between grid items
   */
  spacing?: number;
  /**
   * Whether this is a container grid
   */
  container?: boolean;
  /**
   * Grid size for different breakpoints
   */
  xs?: number | 'auto';
  sm?: number | 'auto';
  md?: number | 'auto';
  lg?: number | 'auto';
  xl?: number | 'auto';
}

/**
 * A Material-UI Grid component using the new Grid2 system for responsive layouts
 */
export const MuiGrid: React.FC<MuiGridProps> = ({
  children,
  container = false,
  spacing = 2,
  xs,
  sm,
  md,
  lg,
  xl,
  ...props
}) => {
  return (
    <Grid
      container={container}
      spacing={container ? spacing : undefined}
      xs={xs}
      sm={sm}
      md={md}
      lg={lg}
      xl={xl}
      {...props}
    >
      {children}
    </Grid>
  );
};
