import React from 'react';
import { Stack, StackProps, Divider } from '@mui/material';

export interface MuiStackProps extends StackProps {
  /**
   * The content of the stack
   */
  children: React.ReactNode;
  /**
   * The direction of the stack
   */
  direction?: 'row' | 'row-reverse' | 'column' | 'column-reverse';
  /**
   * The spacing between items
   */
  spacing?: number | string;
  /**
   * Whether to show dividers between items
   */
  divider?: React.ReactElement | boolean;
  /**
   * How to align items
   */
  alignItems?: 'flex-start' | 'center' | 'flex-end' | 'stretch' | 'baseline';
  /**
   * How to justify content
   */
  justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  /**
   * Whether items should wrap
   */
  flexWrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
  /**
   * Whether the stack should take full width
   */
  fullWidth?: boolean;
  /**
   * Whether the stack should take full height
   */
  fullHeight?: boolean;
}

/**
 * A Material-UI Stack component for one-dimensional layouts
 */
export const MuiStack: React.FC<MuiStackProps> = ({
  children,
  direction = 'column',
  spacing = 1,
  divider,
  alignItems,
  justifyContent,
  flexWrap,
  fullWidth = false,
  fullHeight = false,
  sx,
  ...props
}) => {
  const getDivider = () => {
    if (divider === true) {
      return <Divider orientation={direction === 'row' || direction === 'row-reverse' ? 'vertical' : 'horizontal'} flexItem />;
    }
    return divider;
  };

  const customSx = {
    ...(fullWidth && { width: '100%' }),
    ...(fullHeight && { height: '100%' }),
    ...(alignItems && { alignItems }),
    ...(justifyContent && { justifyContent }),
    ...(flexWrap && { flexWrap }),
    ...sx,
  };

  return (
    <Stack
      direction={direction}
      spacing={spacing}
      divider={getDivider()}
      sx={customSx}
      {...props}
    >
      {children}
    </Stack>
  );
};
