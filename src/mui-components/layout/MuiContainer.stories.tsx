import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MuiContainer } from './MuiContainer';
import { Typography, Paper, Box, Button } from '@mui/material';

const meta: Meta<typeof MuiContainer> = {
  title: 'MUI Components/Layout/Container',
  component: MuiContainer,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'A Material-UI Container component for centering content horizontally with responsive max-widths.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    maxWidth: {
      control: { type: 'select' },
      options: ['xs', 'sm', 'md', 'lg', 'xl', false],
    },
  },
};

export default meta;
type Story = StoryObj<typeof MuiContainer>;

export const Default: Story = {
  args: {
    children: (
      <Paper sx={{ p: 3, bgcolor: 'primary.light' }}>
        <Typography variant="h4" gutterBottom color="white">
          Default Container (lg)
        </Typography>
        <Typography variant="body1" color="white">
          This container has a maximum width of 'lg' and centers content horizontally.
        </Typography>
      </Paper>
    ),
  },
};

export const MaxWidths: Story = {
  render: () => (
    <Box sx={{ bgcolor: 'grey.100', minHeight: '100vh', py: 4 }}>
      <MuiContainer maxWidth="xs" sx={{ mb: 4 }}>
        <Paper sx={{ p: 2, bgcolor: 'primary.main', color: 'white', textAlign: 'center' }}>
          <Typography variant="h6">XS Container</Typography>
          <Typography variant="body2">Max width: 444px</Typography>
        </Paper>
      </MuiContainer>
      
      <MuiContainer maxWidth="sm" sx={{ mb: 4 }}>
        <Paper sx={{ p: 2, bgcolor: 'secondary.main', color: 'white', textAlign: 'center' }}>
          <Typography variant="h6">SM Container</Typography>
          <Typography variant="body2">Max width: 600px</Typography>
        </Paper>
      </MuiContainer>
      
      <MuiContainer maxWidth="md" sx={{ mb: 4 }}>
        <Paper sx={{ p: 2, bgcolor: 'success.main', color: 'white', textAlign: 'center' }}>
          <Typography variant="h6">MD Container</Typography>
          <Typography variant="body2">Max width: 900px</Typography>
        </Paper>
      </MuiContainer>
      
      <MuiContainer maxWidth="lg" sx={{ mb: 4 }}>
        <Paper sx={{ p: 2, bgcolor: 'warning.main', color: 'white', textAlign: 'center' }}>
          <Typography variant="h6">LG Container</Typography>
          <Typography variant="body2">Max width: 1200px</Typography>
        </Paper>
      </MuiContainer>
      
      <MuiContainer maxWidth="xl">
        <Paper sx={{ p: 2, bgcolor: 'error.main', color: 'white', textAlign: 'center' }}>
          <Typography variant="h6">XL Container</Typography>
          <Typography variant="body2">Max width: 1536px</Typography>
        </Paper>
      </MuiContainer>
    </Box>
  ),
};

export const Fixed: Story = {
  render: () => (
    <Box sx={{ bgcolor: 'grey.100', minHeight: '100vh', py: 4 }}>
      <MuiContainer maxWidth="md" fixed sx={{ mb: 4 }}>
        <Paper sx={{ p: 3, bgcolor: 'info.main', color: 'white' }}>
          <Typography variant="h5" gutterBottom>
            Fixed Container
          </Typography>
          <Typography variant="body1">
            This container has a fixed width that doesn't change based on the viewport size.
            It maintains the same width regardless of screen size.
          </Typography>
        </Paper>
      </MuiContainer>
      
      <MuiContainer maxWidth="md" sx={{ mb: 4 }}>
        <Paper sx={{ p: 3, bgcolor: 'success.main', color: 'white' }}>
          <Typography variant="h5" gutterBottom>
            Fluid Container (Default)
          </Typography>
          <Typography variant="body1">
            This container is fluid and adapts to the viewport size up to the maximum width.
            It will be smaller on smaller screens.
          </Typography>
        </Paper>
      </MuiContainer>
    </Box>
  ),
};

export const DisableGutters: Story = {
  render: () => (
    <Box sx={{ bgcolor: 'grey.100', minHeight: '100vh', py: 4 }}>
      <MuiContainer maxWidth="md" sx={{ mb: 4 }}>
        <Paper sx={{ p: 3, bgcolor: 'primary.main', color: 'white' }}>
          <Typography variant="h6" gutterBottom>
            With Gutters (Default)
          </Typography>
          <Typography variant="body2">
            This container has default padding/gutters on the sides.
          </Typography>
        </Paper>
      </MuiContainer>
      
      <MuiContainer maxWidth="md" disableGutters>
        <Paper sx={{ p: 3, bgcolor: 'secondary.main', color: 'white' }}>
          <Typography variant="h6" gutterBottom>
            Without Gutters
          </Typography>
          <Typography variant="body2">
            This container has no padding/gutters and extends to the edges.
          </Typography>
        </Paper>
      </MuiContainer>
    </Box>
  ),
};

export const Centered: Story = {
  args: {
    maxWidth: 'sm',
    centered: true,
    sx: { minHeight: '50vh' },
    children: (
      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h4" gutterBottom>
          Centered Content
        </Typography>
        <Typography variant="body1" paragraph>
          This content is centered both horizontally and vertically within the container.
        </Typography>
        <Button variant="contained" color="primary">
          Call to Action
        </Button>
      </Paper>
    ),
  },
};

export const FullWidth: Story = {
  args: {
    maxWidth: false,
    children: (
      <Paper sx={{ p: 3, bgcolor: 'warning.light' }}>
        <Typography variant="h5" gutterBottom>
          Full Width Container
        </Typography>
        <Typography variant="body1">
          This container has no maximum width constraint and will take the full width of its parent.
        </Typography>
      </Paper>
    ),
  },
};

export const WithCustomPadding: Story = {
  args: {
    maxWidth: 'md',
    padding: 4,
    children: (
      <Paper sx={{ p: 3, bgcolor: 'success.light' }}>
        <Typography variant="h5" gutterBottom>
          Custom Padding Container
        </Typography>
        <Typography variant="body1">
          This container has custom padding applied in addition to the default gutters.
        </Typography>
      </Paper>
    ),
  },
};

export const ArticleLayout: Story = {
  render: () => (
    <Box sx={{ bgcolor: 'grey.50', minHeight: '100vh', py: 4 }}>
      <MuiContainer maxWidth="md">
        <Paper sx={{ p: 4 }}>
          <Typography variant="h3" gutterBottom>
            Article Title
          </Typography>
          <Typography variant="subtitle1" color="text.secondary" gutterBottom>
            Published on March 15, 2024
          </Typography>
          
          <Typography variant="body1" paragraph>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod 
            tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, 
            quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
          </Typography>
          
          <Typography variant="body1" paragraph>
            Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore 
            eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, 
            sunt in culpa qui officia deserunt mollit anim id est laborum.
          </Typography>
          
          <Typography variant="body1" paragraph>
            Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium 
            doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore 
            veritatis et quasi architecto beatae vitae dicta sunt explicabo.
          </Typography>
        </Paper>
      </MuiContainer>
    </Box>
  ),
};
