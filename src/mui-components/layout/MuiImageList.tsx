import React from 'react';
import {
  ImageList,
  ImageListItem,
  ImageListItemBar,
  ImageListProps,
  IconButton,
} from '@mui/material';
import { Info as InfoIcon } from '@mui/icons-material';

export interface ImageItem {
  img: string;
  title: string;
  author?: string;
  featured?: boolean;
  rows?: number;
  cols?: number;
}

export interface MuiImageListProps extends Omit<ImageListProps, 'children'> {
  /**
   * Array of image items
   */
  items: ImageItem[];
  /**
   * Number of columns
   */
  cols?: number;
  /**
   * Row height in pixels
   */
  rowHeight?: number;
  /**
   * Gap between items
   */
  gap?: number;
  /**
   * Whether to show item bars
   */
  showItemBar?: boolean;
  /**
   * Position of the item bar
   */
  itemBarPosition?: 'below' | 'top' | 'bottom';
  /**
   * Callback when item is clicked
   */
  onItemClick?: (item: ImageItem, index: number) => void;
  /**
   * Callback when info button is clicked
   */
  onInfoClick?: (item: ImageItem, index: number) => void;
}

/**
 * A Material-UI ImageList component for displaying collections of images
 */
export const MuiImageList: React.FC<MuiImageListProps> = ({
  items,
  cols = 4,
  rowHeight = 164,
  gap = 4,
  showItemBar = false,
  itemBarPosition = 'bottom',
  onItemClick,
  onInfoClick,
  variant = 'standard',
  ...props
}) => {
  return (
    <ImageList
      cols={cols}
      rowHeight={rowHeight}
      gap={gap}
      variant={variant}
      {...props}
    >
      {items.map((item, index) => (
        <ImageListItem
          key={index}
          cols={item.cols || 1}
          rows={item.rows || 1}
          onClick={() => onItemClick?.(item, index)}
          sx={{ cursor: onItemClick ? 'pointer' : 'default' }}
        >
          <img
            src={item.img}
            alt={item.title}
            loading="lazy"
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
            }}
          />
          {showItemBar && (
            <ImageListItemBar
              title={item.title}
              subtitle={item.author}
              position={itemBarPosition}
              actionIcon={
                onInfoClick && (
                  <IconButton
                    sx={{ color: 'rgba(255, 255, 255, 0.54)' }}
                    aria-label={`info about ${item.title}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      onInfoClick(item, index);
                    }}
                  >
                    <InfoIcon />
                  </IconButton>
                )
              }
            />
          )}
        </ImageListItem>
      ))}
    </ImageList>
  );
};
