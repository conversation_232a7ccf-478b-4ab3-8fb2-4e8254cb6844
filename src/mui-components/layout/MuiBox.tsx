import React from 'react';
import { Box, BoxProps } from '@mui/material';

export interface MuiBoxProps extends BoxProps {
  /**
   * The content of the box
   */
  children?: React.ReactNode;
  /**
   * Display property
   */
  display?: 'block' | 'inline' | 'inline-block' | 'flex' | 'inline-flex' | 'grid' | 'inline-grid' | 'none';
  /**
   * Flex direction
   */
  flexDirection?: 'row' | 'row-reverse' | 'column' | 'column-reverse';
  /**
   * Justify content
   */
  justifyContent?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';
  /**
   * Align items
   */
  alignItems?: 'flex-start' | 'flex-end' | 'center' | 'baseline' | 'stretch';
  /**
   * Gap between items
   */
  gap?: number | string;
  /**
   * Padding
   */
  padding?: number | string;
  /**
   * Margin
   */
  margin?: number | string;
  /**
   * Width
   */
  width?: number | string;
  /**
   * Height
   */
  height?: number | string;
  /**
   * Background color
   */
  bgcolor?: string;
  /**
   * Border radius
   */
  borderRadius?: number | string;
  /**
   * Whether to center content
   */
  centered?: boolean;
}

/**
 * A Material-UI Box component for layout and styling
 */
export const MuiBox: React.FC<MuiBoxProps> = ({
  children,
  display = 'block',
  flexDirection,
  justifyContent,
  alignItems,
  gap,
  padding,
  margin,
  width,
  height,
  bgcolor,
  borderRadius,
  centered = false,
  sx,
  ...props
}) => {
  const customSx = {
    display,
    ...(display === 'flex' && {
      flexDirection,
      justifyContent: centered ? 'center' : justifyContent,
      alignItems: centered ? 'center' : alignItems,
    }),
    ...(gap && { gap }),
    ...(padding && { p: padding }),
    ...(margin && { m: margin }),
    ...(width && { width }),
    ...(height && { height }),
    ...(bgcolor && { bgcolor }),
    ...(borderRadius && { borderRadius }),
    ...sx,
  };

  return (
    <Box sx={customSx} {...props}>
      {children}
    </Box>
  );
};
