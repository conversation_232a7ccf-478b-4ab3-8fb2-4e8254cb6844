import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MuiImageList } from './MuiImageList';

const meta: Meta<typeof MuiImageList> = {
  title: 'MUI Components/Layout/ImageList',
  component: MuiImageList,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI ImageList component for displaying collections of images in a grid layout.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['masonry', 'quilted', 'standard', 'woven'],
    },
    itemBarPosition: {
      control: { type: 'select' },
      options: ['below', 'top', 'bottom'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof MuiImageList>;

const sampleImages = [
  {
    img: 'https://images.unsplash.com/photo-1551963831-b3b1ca40c98e?w=248&fit=crop&auto=format',
    title: 'Breakfast',
    author: '@bkristastucchio',
  },
  {
    img: 'https://images.unsplash.com/photo-1551782450-a2132b4ba21d?w=248&fit=crop&auto=format',
    title: 'Burger',
    author: '@rollelflex_graphy726',
  },
  {
    img: 'https://images.unsplash.com/photo-1522770179533-24471fcdba45?w=248&fit=crop&auto=format',
    title: 'Camera',
    author: '@helloimnik',
  },
  {
    img: 'https://images.unsplash.com/photo-1444418776041-9c7e33cc5a9c?w=248&fit=crop&auto=format',
    title: 'Coffee',
    author: '@nolanissac',
  },
  {
    img: 'https://images.unsplash.com/photo-1533827432537-70133748f5c8?w=248&fit=crop&auto=format',
    title: 'Hats',
    author: '@hjrc33',
  },
  {
    img: 'https://images.unsplash.com/photo-1558642452-9d2a7deb7f62?w=248&fit=crop&auto=format',
    title: 'Honey',
    author: '@arwinneil',
  },
  {
    img: 'https://images.unsplash.com/photo-1516802273409-68526ee1bdd6?w=248&fit=crop&auto=format',
    title: 'Basketball',
    author: '@tjdragotta',
  },
  {
    img: 'https://images.unsplash.com/photo-1518756131217-31eb79b20e8f?w=248&fit=crop&auto=format',
    title: 'Fern',
    author: '@katie_wasserman',
  },
];

export const Default: Story = {
  args: {
    items: sampleImages.slice(0, 4),
    cols: 2,
    rowHeight: 164,
    sx: { width: 500, height: 350 },
  },
};

export const WithItemBars: Story = {
  args: {
    items: sampleImages,
    cols: 3,
    rowHeight: 164,
    showItemBar: true,
    onInfoClick: (item) => alert(`Info clicked for: ${item.title}`),
    sx: { width: 600, height: 450 },
  },
};

export const Clickable: Story = {
  args: {
    items: sampleImages,
    cols: 4,
    rowHeight: 164,
    showItemBar: true,
    onItemClick: (item) => alert(`Clicked: ${item.title}`),
    onInfoClick: (item) => alert(`Info: ${item.title}`),
    sx: { width: 800, height: 400 },
  },
};

export const Quilted: Story = {
  args: {
    items: [
      { ...sampleImages[0], rows: 2, cols: 2, featured: true },
      { ...sampleImages[1] },
      { ...sampleImages[2] },
      { ...sampleImages[3], rows: 2 },
      { ...sampleImages[4] },
      { ...sampleImages[5], cols: 2 },
      { ...sampleImages[6] },
      { ...sampleImages[7] },
    ],
    variant: 'quilted',
    cols: 4,
    rowHeight: 121,
    showItemBar: true,
    sx: { width: 600, height: 450 },
  },
};

export const Masonry: Story = {
  args: {
    items: sampleImages,
    variant: 'masonry',
    cols: 3,
    gap: 8,
    showItemBar: true,
    itemBarPosition: 'below',
    sx: { width: 600, height: 500 },
  },
};

export const Woven: Story = {
  args: {
    items: sampleImages,
    variant: 'woven',
    cols: 4,
    gap: 2,
    showItemBar: true,
    sx: { width: 600, height: 400 },
  },
};

export const DifferentSizes: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
      <div>
        <h3>Small (2 columns)</h3>
        <MuiImageList
          items={sampleImages.slice(0, 4)}
          cols={2}
          rowHeight={120}
          gap={4}
          sx={{ width: 300, height: 250 }}
        />
      </div>
      
      <div>
        <h3>Medium (3 columns)</h3>
        <MuiImageList
          items={sampleImages.slice(0, 6)}
          cols={3}
          rowHeight={140}
          gap={6}
          showItemBar
          sx={{ width: 450, height: 300 }}
        />
      </div>
      
      <div>
        <h3>Large (4 columns)</h3>
        <MuiImageList
          items={sampleImages}
          cols={4}
          rowHeight={160}
          gap={8}
          showItemBar
          sx={{ width: 600, height: 350 }}
        />
      </div>
    </div>
  ),
};
