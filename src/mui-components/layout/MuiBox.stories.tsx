import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { <PERSON><PERSON><PERSON><PERSON> } from './MuiBox';
import { Typography, Button, Paper } from '@mui/material';

const meta: Meta<typeof MuiBox> = {
  title: 'MUI Components/Layout/Box',
  component: MuiBox,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Box component for layout and styling with system props.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    display: {
      control: { type: 'select' },
      options: ['block', 'inline', 'inline-block', 'flex', 'inline-flex', 'grid', 'inline-grid', 'none'],
    },
    flexDirection: {
      control: { type: 'select' },
      options: ['row', 'row-reverse', 'column', 'column-reverse'],
    },
    justifyContent: {
      control: { type: 'select' },
      options: ['flex-start', 'flex-end', 'center', 'space-between', 'space-around', 'space-evenly'],
    },
    alignItems: {
      control: { type: 'select' },
      options: ['flex-start', 'flex-end', 'center', 'baseline', 'stretch'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof MuiBox>;

export const Default: Story = {
  args: {
    children: (
      <Typography>This is content inside a Box component</Typography>
    ),
    padding: 2,
    bgcolor: 'grey.100',
    borderRadius: 1,
  },
};

export const FlexContainer: Story = {
  args: {
    display: 'flex',
    gap: 2,
    padding: 2,
    bgcolor: 'primary.light',
    borderRadius: 1,
    children: (
      <>
        <Button variant="contained">Button 1</Button>
        <Button variant="contained">Button 2</Button>
        <Button variant="contained">Button 3</Button>
      </>
    ),
  },
};

export const FlexColumn: Story = {
  args: {
    display: 'flex',
    flexDirection: 'column',
    gap: 2,
    padding: 2,
    bgcolor: 'secondary.light',
    borderRadius: 1,
    width: 200,
    children: (
      <>
        <Button variant="outlined">Top</Button>
        <Button variant="outlined">Middle</Button>
        <Button variant="outlined">Bottom</Button>
      </>
    ),
  },
};

export const Centered: Story = {
  args: {
    display: 'flex',
    centered: true,
    width: 300,
    height: 200,
    bgcolor: 'info.light',
    borderRadius: 2,
    children: (
      <Typography variant="h6" color="white">
        Centered Content
      </Typography>
    ),
  },
};

export const JustifyContent: Story = {
  render: () => (
    <div style={{ width: '100%' }}>
      <MuiBox
        display="flex"
        justifyContent="flex-start"
        gap={1}
        padding={2}
        bgcolor="grey.100"
        borderRadius={1}
        sx={{ mb: 2 }}
      >
        <Button size="small">Start</Button>
        <Button size="small">Items</Button>
      </MuiBox>
      
      <MuiBox
        display="flex"
        justifyContent="center"
        gap={1}
        padding={2}
        bgcolor="grey.100"
        borderRadius={1}
        sx={{ mb: 2 }}
      >
        <Button size="small">Center</Button>
        <Button size="small">Items</Button>
      </MuiBox>
      
      <MuiBox
        display="flex"
        justifyContent="flex-end"
        gap={1}
        padding={2}
        bgcolor="grey.100"
        borderRadius={1}
        sx={{ mb: 2 }}
      >
        <Button size="small">End</Button>
        <Button size="small">Items</Button>
      </MuiBox>
      
      <MuiBox
        display="flex"
        justifyContent="space-between"
        gap={1}
        padding={2}
        bgcolor="grey.100"
        borderRadius={1}
        sx={{ mb: 2 }}
      >
        <Button size="small">Space</Button>
        <Button size="small">Between</Button>
      </MuiBox>
      
      <MuiBox
        display="flex"
        justifyContent="space-around"
        gap={1}
        padding={2}
        bgcolor="grey.100"
        borderRadius={1}
      >
        <Button size="small">Space</Button>
        <Button size="small">Around</Button>
      </MuiBox>
    </div>
  ),
};

export const AlignItems: Story = {
  render: () => (
    <div style={{ width: '100%' }}>
      <MuiBox
        display="flex"
        alignItems="flex-start"
        gap={1}
        padding={2}
        bgcolor="grey.100"
        borderRadius={1}
        height={100}
        sx={{ mb: 2 }}
      >
        <Button>Flex Start</Button>
        <Typography variant="h6">Tall Content</Typography>
      </MuiBox>
      
      <MuiBox
        display="flex"
        alignItems="center"
        gap={1}
        padding={2}
        bgcolor="grey.100"
        borderRadius={1}
        height={100}
        sx={{ mb: 2 }}
      >
        <Button>Center</Button>
        <Typography variant="h6">Tall Content</Typography>
      </MuiBox>
      
      <MuiBox
        display="flex"
        alignItems="flex-end"
        gap={1}
        padding={2}
        bgcolor="grey.100"
        borderRadius={1}
        height={100}
      >
        <Button>Flex End</Button>
        <Typography variant="h6">Tall Content</Typography>
      </MuiBox>
    </div>
  ),
};

export const ResponsiveBox: Story = {
  args: {
    display: 'flex',
    flexDirection: { xs: 'column', sm: 'row' },
    gap: 2,
    padding: 2,
    bgcolor: 'success.light',
    borderRadius: 1,
    children: (
      <>
        <Button variant="contained">Responsive</Button>
        <Button variant="contained">Layout</Button>
        <Button variant="contained">Box</Button>
      </>
    ),
  },
};

export const NestedBoxes: Story = {
  render: () => (
    <MuiBox
      display="flex"
      flexDirection="column"
      gap={2}
      padding={3}
      bgcolor="primary.light"
      borderRadius={2}
      width={400}
    >
      <Typography variant="h6" color="white">
        Outer Box
      </Typography>
      
      <MuiBox
        display="flex"
        gap={1}
        padding={2}
        bgcolor="white"
        borderRadius={1}
      >
        <Paper sx={{ p: 1, flex: 1 }}>
          <Typography variant="body2">Nested 1</Typography>
        </Paper>
        <Paper sx={{ p: 1, flex: 1 }}>
          <Typography variant="body2">Nested 2</Typography>
        </Paper>
      </MuiBox>
      
      <MuiBox
        padding={2}
        bgcolor="white"
        borderRadius={1}
      >
        <Typography variant="body1">
          Another nested box with different content
        </Typography>
      </MuiBox>
    </MuiBox>
  ),
};

export const GridLayout: Story = {
  render: () => (
    <MuiBox
      display="grid"
      sx={{
        gridTemplateColumns: 'repeat(3, 1fr)',
        gap: 2,
        width: 400,
      }}
    >
      {Array.from({ length: 6 }, (_, index) => (
        <Paper key={index} sx={{ p: 2, textAlign: 'center' }}>
          <Typography>Item {index + 1}</Typography>
        </Paper>
      ))}
    </MuiBox>
  ),
};
