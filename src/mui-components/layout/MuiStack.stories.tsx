import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { MuiStack } from './MuiStack';
import { 
  Button, 
  Paper, 
  Typography, 
  Chip, 
  Avatar,
  Box,
  Card,
  CardContent,
} from '@mui/material';

const meta: Meta<typeof MuiStack> = {
  title: 'MUI Components/Layout/Stack',
  component: MuiStack,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Material-UI Stack component for one-dimensional layouts with consistent spacing.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    direction: {
      control: { type: 'select' },
      options: ['row', 'row-reverse', 'column', 'column-reverse'],
    },
    alignItems: {
      control: { type: 'select' },
      options: ['flex-start', 'center', 'flex-end', 'stretch', 'baseline'],
    },
    justifyContent: {
      control: { type: 'select' },
      options: ['flex-start', 'center', 'flex-end', 'space-between', 'space-around', 'space-evenly'],
    },
    flexWrap: {
      control: { type: 'select' },
      options: ['nowrap', 'wrap', 'wrap-reverse'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof MuiStack>;

export const Default: Story = {
  args: {
    children: (
      <>
        <Button variant="contained">Button 1</Button>
        <Button variant="contained">Button 2</Button>
        <Button variant="contained">Button 3</Button>
      </>
    ),
  },
};

export const Horizontal: Story = {
  args: {
    direction: 'row',
    spacing: 2,
    children: (
      <>
        <Button variant="outlined">First</Button>
        <Button variant="outlined">Second</Button>
        <Button variant="outlined">Third</Button>
      </>
    ),
  },
};

export const WithDivider: Story = {
  args: {
    direction: 'row',
    spacing: 2,
    divider: true,
    children: (
      <>
        <Typography>Item 1</Typography>
        <Typography>Item 2</Typography>
        <Typography>Item 3</Typography>
      </>
    ),
  },
};

export const DifferentSpacing: Story = {
  render: () => (
    <Box sx={{ width: '100%' }}>
      <Typography variant="h6" gutterBottom>Spacing = 0</Typography>
      <MuiStack direction="row" spacing={0} sx={{ mb: 3 }}>
        <Paper sx={{ p: 1 }}>Item 1</Paper>
        <Paper sx={{ p: 1 }}>Item 2</Paper>
        <Paper sx={{ p: 1 }}>Item 3</Paper>
      </MuiStack>
      
      <Typography variant="h6" gutterBottom>Spacing = 1</Typography>
      <MuiStack direction="row" spacing={1} sx={{ mb: 3 }}>
        <Paper sx={{ p: 1 }}>Item 1</Paper>
        <Paper sx={{ p: 1 }}>Item 2</Paper>
        <Paper sx={{ p: 1 }}>Item 3</Paper>
      </MuiStack>
      
      <Typography variant="h6" gutterBottom>Spacing = 3</Typography>
      <MuiStack direction="row" spacing={3} sx={{ mb: 3 }}>
        <Paper sx={{ p: 1 }}>Item 1</Paper>
        <Paper sx={{ p: 1 }}>Item 2</Paper>
        <Paper sx={{ p: 1 }}>Item 3</Paper>
      </MuiStack>
      
      <Typography variant="h6" gutterBottom>Spacing = "2rem"</Typography>
      <MuiStack direction="row" spacing="2rem">
        <Paper sx={{ p: 1 }}>Item 1</Paper>
        <Paper sx={{ p: 1 }}>Item 2</Paper>
        <Paper sx={{ p: 1 }}>Item 3</Paper>
      </MuiStack>
    </Box>
  ),
};

export const Alignment: Story = {
  render: () => (
    <Box sx={{ width: 400 }}>
      <Typography variant="h6" gutterBottom>Align Items: flex-start</Typography>
      <MuiStack 
        direction="row" 
        spacing={2} 
        alignItems="flex-start"
        sx={{ mb: 3, height: 80, bgcolor: 'grey.100', p: 1 }}
      >
        <Button>Short</Button>
        <Button sx={{ height: 60 }}>Tall Button</Button>
        <Button>Short</Button>
      </MuiStack>
      
      <Typography variant="h6" gutterBottom>Align Items: center</Typography>
      <MuiStack 
        direction="row" 
        spacing={2} 
        alignItems="center"
        sx={{ mb: 3, height: 80, bgcolor: 'grey.100', p: 1 }}
      >
        <Button>Short</Button>
        <Button sx={{ height: 60 }}>Tall Button</Button>
        <Button>Short</Button>
      </MuiStack>
      
      <Typography variant="h6" gutterBottom>Align Items: flex-end</Typography>
      <MuiStack 
        direction="row" 
        spacing={2} 
        alignItems="flex-end"
        sx={{ height: 80, bgcolor: 'grey.100', p: 1 }}
      >
        <Button>Short</Button>
        <Button sx={{ height: 60 }}>Tall Button</Button>
        <Button>Short</Button>
      </MuiStack>
    </Box>
  ),
};

export const JustifyContent: Story = {
  render: () => (
    <Box sx={{ width: 400 }}>
      <Typography variant="h6" gutterBottom>Justify: flex-start</Typography>
      <MuiStack 
        direction="row" 
        spacing={1} 
        justifyContent="flex-start"
        sx={{ mb: 2, bgcolor: 'grey.100', p: 1 }}
      >
        <Button size="small">A</Button>
        <Button size="small">B</Button>
      </MuiStack>
      
      <Typography variant="h6" gutterBottom>Justify: center</Typography>
      <MuiStack 
        direction="row" 
        spacing={1} 
        justifyContent="center"
        sx={{ mb: 2, bgcolor: 'grey.100', p: 1 }}
      >
        <Button size="small">A</Button>
        <Button size="small">B</Button>
      </MuiStack>
      
      <Typography variant="h6" gutterBottom>Justify: space-between</Typography>
      <MuiStack 
        direction="row" 
        spacing={1} 
        justifyContent="space-between"
        sx={{ bgcolor: 'grey.100', p: 1 }}
      >
        <Button size="small">A</Button>
        <Button size="small">B</Button>
      </MuiStack>
    </Box>
  ),
};

export const Wrapping: Story = {
  args: {
    direction: 'row',
    spacing: 1,
    flexWrap: 'wrap',
    sx: { width: 200 },
    children: (
      <>
        {Array.from({ length: 8 }, (_, index) => (
          <Chip key={index} label={`Tag ${index + 1}`} />
        ))}
      </>
    ),
  },
};

export const UserProfile: Story = {
  render: () => (
    <Card sx={{ maxWidth: 300 }}>
      <CardContent>
        <MuiStack spacing={2} alignItems="center">
          <Avatar sx={{ width: 80, height: 80 }}>JD</Avatar>
          
          <MuiStack spacing={0.5} alignItems="center">
            <Typography variant="h5">John Doe</Typography>
            <Typography variant="body2" color="text.secondary">
              Software Engineer
            </Typography>
          </MuiStack>
          
          <MuiStack direction="row" spacing={1} flexWrap="wrap" justifyContent="center">
            <Chip label="React" size="small" />
            <Chip label="TypeScript" size="small" />
            <Chip label="Node.js" size="small" />
          </MuiStack>
          
          <MuiStack direction="row" spacing={1} fullWidth>
            <Button variant="outlined" fullWidth>
              Message
            </Button>
            <Button variant="contained" fullWidth>
              Connect
            </Button>
          </MuiStack>
        </MuiStack>
      </CardContent>
    </Card>
  ),
};

export const ResponsiveStack: Story = {
  args: {
    direction: { xs: 'column', sm: 'row' },
    spacing: 2,
    children: (
      <>
        <Paper sx={{ p: 2, flex: 1 }}>
          <Typography>Responsive item 1</Typography>
        </Paper>
        <Paper sx={{ p: 2, flex: 1 }}>
          <Typography>Responsive item 2</Typography>
        </Paper>
        <Paper sx={{ p: 2, flex: 1 }}>
          <Typography>Responsive item 3</Typography>
        </Paper>
      </>
    ),
  },
};

export const NestedStacks: Story = {
  render: () => (
    <Paper sx={{ p: 3, width: 400 }}>
      <MuiStack spacing={3}>
        <Typography variant="h5">Nested Stacks Example</Typography>
        
        <MuiStack direction="row" spacing={2} justifyContent="space-between">
          <MuiStack spacing={1}>
            <Typography variant="h6">Left Column</Typography>
            <Button variant="outlined" size="small">Action 1</Button>
            <Button variant="outlined" size="small">Action 2</Button>
          </MuiStack>
          
          <MuiStack spacing={1} alignItems="flex-end">
            <Typography variant="h6">Right Column</Typography>
            <Button variant="contained" size="small">Primary</Button>
            <Button variant="text" size="small">Secondary</Button>
          </MuiStack>
        </MuiStack>
        
        <MuiStack direction="row" spacing={1} justifyContent="center">
          <Chip label="Tag 1" />
          <Chip label="Tag 2" />
          <Chip label="Tag 3" />
        </MuiStack>
      </MuiStack>
    </Paper>
  ),
};
