import type { Meta, StoryObj } from '@storybook/react';
import { MuiGrid } from './MuiGrid';
import { Paper, Typography, Box } from '@mui/material';

const meta: Meta<typeof MuiGrid> = {
  title: 'MUI Components/Layout/Grid',
  component: MuiGrid,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'A Material-UI Grid component using the new Grid2 system for creating responsive layouts.',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

const GridItem = ({ children, ...props }: any) => (
  <MuiGrid {...props}>
    <Paper sx={{ p: 2, textAlign: 'center', backgroundColor: 'primary.light', color: 'white' }}>
      {children}
    </Paper>
  </MuiGrid>
);

export const BasicGrid: Story = {
  render: () => (
    <MuiGrid container spacing={2}>
      <GridItem xs={12}>
        <Typography>xs=12</Typography>
      </GridItem>
      <GridItem xs={6}>
        <Typography>xs=6</Typography>
      </GridItem>
      <GridItem xs={6}>
        <Typography>xs=6</Typography>
      </GridItem>
      <GridItem xs={3}>
        <Typography>xs=3</Typography>
      </GridItem>
      <GridItem xs={3}>
        <Typography>xs=3</Typography>
      </GridItem>
      <GridItem xs={3}>
        <Typography>xs=3</Typography>
      </GridItem>
      <GridItem xs={3}>
        <Typography>xs=3</Typography>
      </GridItem>
    </MuiGrid>
  ),
};

export const ResponsiveGrid: Story = {
  render: () => (
    <MuiGrid container spacing={2}>
      <GridItem xs={12} sm={6} md={4} lg={3}>
        <Typography variant="body2">
          xs=12 sm=6 md=4 lg=3
        </Typography>
      </GridItem>
      <GridItem xs={12} sm={6} md={4} lg={3}>
        <Typography variant="body2">
          xs=12 sm=6 md=4 lg=3
        </Typography>
      </GridItem>
      <GridItem xs={12} sm={6} md={4} lg={3}>
        <Typography variant="body2">
          xs=12 sm=6 md=4 lg=3
        </Typography>
      </GridItem>
      <GridItem xs={12} sm={6} md={4} lg={3}>
        <Typography variant="body2">
          xs=12 sm=6 md=4 lg=3
        </Typography>
      </GridItem>
    </MuiGrid>
  ),
};

export const NestedGrid: Story = {
  render: () => (
    <MuiGrid container spacing={2}>
      <MuiGrid xs={12} md={8}>
        <Paper sx={{ p: 2, backgroundColor: 'secondary.light' }}>
          <Typography variant="h6" gutterBottom>
            Main Content (xs=12 md=8)
          </Typography>
          <MuiGrid container spacing={1}>
            <GridItem xs={6}>
              <Typography variant="body2">Nested xs=6</Typography>
            </GridItem>
            <GridItem xs={6}>
              <Typography variant="body2">Nested xs=6</Typography>
            </GridItem>
          </MuiGrid>
        </Paper>
      </MuiGrid>
      <MuiGrid xs={12} md={4}>
        <Paper sx={{ p: 2, backgroundColor: 'info.light', color: 'white' }}>
          <Typography variant="h6">
            Sidebar (xs=12 md=4)
          </Typography>
        </Paper>
      </MuiGrid>
    </MuiGrid>
  ),
};

export const AutoSizing: Story = {
  render: () => (
    <MuiGrid container spacing={2}>
      <GridItem xs="auto">
        <Typography>Auto width</Typography>
      </GridItem>
      <GridItem xs={6}>
        <Typography>xs=6</Typography>
      </GridItem>
      <GridItem xs="auto">
        <Typography>Auto width</Typography>
      </GridItem>
    </MuiGrid>
  ),
};

export const DifferentSpacing: Story = {
  render: () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Spacing = 1
      </Typography>
      <MuiGrid container spacing={1} sx={{ mb: 4 }}>
        <GridItem xs={4}><Typography>Item 1</Typography></GridItem>
        <GridItem xs={4}><Typography>Item 2</Typography></GridItem>
        <GridItem xs={4}><Typography>Item 3</Typography></GridItem>
      </MuiGrid>
      
      <Typography variant="h6" gutterBottom>
        Spacing = 3
      </Typography>
      <MuiGrid container spacing={3} sx={{ mb: 4 }}>
        <GridItem xs={4}><Typography>Item 1</Typography></GridItem>
        <GridItem xs={4}><Typography>Item 2</Typography></GridItem>
        <GridItem xs={4}><Typography>Item 3</Typography></GridItem>
      </MuiGrid>
      
      <Typography variant="h6" gutterBottom>
        Spacing = 5
      </Typography>
      <MuiGrid container spacing={5}>
        <GridItem xs={4}><Typography>Item 1</Typography></GridItem>
        <GridItem xs={4}><Typography>Item 2</Typography></GridItem>
        <GridItem xs={4}><Typography>Item 3</Typography></GridItem>
      </MuiGrid>
    </Box>
  ),
};
