import { useState } from 'react';
import { useCodebaseAnalyzer } from '@/hooks/useCodebaseAnalyzer';
import { useStorybookParser } from '@/hooks/useStorybookParser';
import { useCodeGenerator, GenerationConfig } from '@/hooks/useCodeGenerator';
import { ConfigurationPanel, ConfigurationData } from '@/components/ConfigurationPanel';
import { AnalysisResults } from '@/components/AnalysisResults';
import { CodeGenerationPanel } from '@/components/CodeGenerationPanel';
import { Separator } from '@/components/ui/separator';
import { Code2, Zap, Brain } from 'lucide-react';

const Index = () => {
  const [currentStep, setCurrentStep] = useState<'config' | 'analysis' | 'generation'>('config');
  const [configuration, setConfiguration] = useState<ConfigurationData | null>(null);
  
  const { analysis, isAnalyzing, analyzeCodebase } = useCodebaseAnalyzer();
  const { storybookData, isParsing, parseStorybook } = useStorybookParser();
  const { generatedCode, isGenerating, generateCode } = useCodeGenerator();

  const handleAnalyze = async (config: ConfigurationData) => {
    setConfiguration(config);
    await Promise.all([
      analyzeCodebase(config.projectPath),
      parseStorybook(config.storybookUrl)
    ]);
    setCurrentStep('analysis');
  };

  const handleCodeGeneration = async (figmaUrl: string, atomicLevel: string) => {
    if (!analysis || !storybookData || !configuration?.figmaApiKey) return;
    
    try {
      // This would use the Figma API in a real implementation
      // For demo purposes, we'll simulate the analysis
      const mockFigmaAnalysis = {
        frame: { id: '1', name: 'UserCard', type: 'FRAME', children: [] },
        extractedComponents: [
          { name: 'UserCard', type: 'card' as const, properties: {}, position: { x: 0, y: 0, width: 300, height: 200 } },
          { name: 'Avatar', type: 'image' as const, properties: {}, position: { x: 16, y: 16, width: 48, height: 48 } },
          { name: 'EditButton', type: 'button' as const, properties: { text: 'Edit' }, position: { x: 200, y: 150, width: 80, height: 32 } }
        ],
        designTokens: [],
        layout: { direction: 'column' as const, gap: 16, padding: { top: 16, right: 16, bottom: 16, left: 16 }, alignment: 'start' }
      };
      
      await generateCode(
        analysis,
        storybookData,
        mockFigmaAnalysis,
        { targetFramework: 'react', atomicLevel: atomicLevel as any }
      );
      setCurrentStep('generation');
    } catch (error) {
      console.error('Code generation failed:', error);
    }
  };

  const isLoading = isAnalyzing || isParsing;

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-primary/10 rounded-lg">
              <img src="/logo.png" alt="Fig to Kode Logo" className="h-8 w-8" />
            </div>
            <h1 className="text-3xl font-bold">Fig to Kode</h1>
          </div>
          <p className="text-muted-foreground">
            Intelligent React code generation from Figma designs using your codebase patterns
          </p>
          
          <div className="flex items-center gap-6 mt-4">
            <div className="flex items-center gap-2">
              <Brain className="h-4 w-4 text-blue-500" />
              <span className="text-sm">Learns from your codebase</span>
            </div>
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-green-500" />
              <span className="text-sm">Production-ready code</span>
            </div>
            <div className="flex items-center gap-2">
              <Code2 className="h-4 w-4 text-purple-500" />
              <span className="text-sm">Auto Storybook integration</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid gap-8 lg:grid-cols-2">
          {/* Left Column - Configuration and Analysis */}
          <div className="space-y-8">
            <ConfigurationPanel 
              onAnalyze={handleAnalyze}
              isLoading={isLoading}
            />
            
            {analysis && storybookData && (
              <>
                <Separator />
                <AnalysisResults 
                  codebaseAnalysis={analysis}
                  storybookData={storybookData}
                />
              </>
            )}
          </div>

          {/* Right Column - Code Generation */}
          <div>
            {currentStep === 'analysis' || currentStep === 'generation' ? (
              <CodeGenerationPanel
                onGenerate={handleCodeGeneration}
                generatedCode={generatedCode}
                isGenerating={isGenerating}
              />
            ) : (
              <div className="h-full flex items-center justify-center">
                <div className="text-center p-8 border rounded-lg bg-muted/50">
                  <Code2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Ready to Generate</h3>
                  <p className="text-muted-foreground">
                    Complete the configuration to start analyzing your codebase and generating intelligent code
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
