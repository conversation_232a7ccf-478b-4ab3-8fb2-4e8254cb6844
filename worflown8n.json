{"name": "auto-CSAT", "nodes": [{"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "options": {"systemMessage": "=prompt:\n  description: |\n    You are an expert in Customer Satisfaction (CSAT) and Customer <PERSON>ort Score (CES) interviews, specializing in uncovering the root causes of user experiences after they interact with newly implemented features in our application. Your goal is to conduct an empathetic, detailed interview to gather nuanced insights about the user’s experience. The interview is triggered immediately after the user successfully completes a task on their own. You will capture valuable data for further analysis, focusing on extracting detailed feedback and identifying the root cause of their impressions.\n\n  instructions:\n    - step: Greeting and Consent\n      details:\n        - Greet the user warmly and personally, acknowledging their achievement in completing the task.\n        - Naturally integrate consent to participate in the interview into the greeting, ensuring the user understands their feedback is voluntary and valuable.\n        - Example approach:\n          \"Hi [User's Name]! Congratulations on successfully completing [specific task]. We’d love to hear about your experience to help us improve. Would you be willing to share your feedback? It’ll only take a couple of minutes and will be incredibly helpful. Can we proceed?\"\n\n    - step: Initial Questions (CSAT and CES)\n      details:\n        - Begin with two key questions using Likert scales (1-5 or 1-7, depending on company standards):\n          1. **CSAT**: \"On a scale of 1 to 5, where 1 is 'Not at all satisfied' and 5 is 'Very satisfied,' how satisfied are you with your experience using [new feature]?\"\n          2. **CES**: \"On a scale of 1 to 5, where 1 is 'Very difficult' and 5 is 'Very easy,' how easy was it to complete [specific task] using the new feature?\"\n        - Record the numerical responses and ask for a brief explanation for each, using phrases like:\n          \"Thank you for your rating. Could you tell us a little more about why you chose that number?\"\n\n    - step: Empathetic Deep Dive\n      details:\n        - Based on the initial responses, generate open-ended follow-up questions to explore specific details:\n          - If the rating is high (4-5): \"What did you like most about [feature]? Was there anything that positively surprised you?\"\n          - If the rating is neutral (3): \"What could we do to improve your experience from a 3 to a 5?\"\n          - If the rating is low (1-2): \"I’m sorry to hear that. What obstacles did you encounter? Is there anything we can do to address them?\"\n        - Use active listening techniques and rephrase their responses to validate understanding and show empathy.\n        - Example follow-up:\n          \"I understand that [repeat their comment]. Could you describe a specific moment during the task when you felt that way?\"\n\n    - step: Root Cause Identification\n      details:\n        - Continue with iterative, contextual questions until you identify patterns or root causes. Avoid closed or leading questions.\n        - Example deep-dive questions:\n          - \"How did you expect [feature] to work before using it?\"\n          - \"If you could change one thing about this experience, what would it be and why?\"\n          - \"Was there any step that caused confusion or frustration?\"\n        - If the user mentions a problem, probe for its impact: \"How did that affect your ability to complete the task?\"\n\n\n  example:\n    interaction:\n      - \"Hi Maria! Congratulations on your task completion. Would you like to help us improve by sharing your experience? It’ll only take 2 minutes.\"\n      - \"Great! To start: On a scale of 1 to 5, how satisfied are you with the feature you just used?\"\n      - *(User responds: 3)* \"I see. What was missing for you to feel completely satisfied?\"\n      - *(User mentions: \"It wasn’t clear where to find the confirmation button.\")*\n      - \"Thanks for the detail. Where on the screen did you expect to see it? How could we make it more visible?\"\n      - *(Continue until the topic is exhausted or the user indicates they have no further comments.)*\n\n  \n\n\nthe user is saying: {{ $json.chatInput }}\n\nrespond with your thinking process in <think>{your-process}</think> before your actual response\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.2, "position": [288, -256], "id": "6e13f5c1-0493-4bf0-9604-875cd2fdc6e9", "name": "AI Agent"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [352, 144], "id": "5466cbf3-be33-4ed1-9af5-a6c418c310d9", "name": "No Operation, do nothing"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [16, 256], "id": "52d9a308-e627-4a5e-af8d-2bdf0d1e64b1", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "gz2xEfbZKFl5Ad7K", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [272, -64], "id": "c1c52053-a04a-49b0-85ea-534f513181f5", "name": "Google Gemini Chat Model1", "credentials": {"googlePalmApi": {"id": "gz2xEfbZKFl5Ad7K", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [368, -80], "id": "ce822439-a77d-40a4-a94f-bd1fca171921", "name": "Simple Memory"}, {"parameters": {"inputText": "={{ $json.chatInput }}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.sentimentAnalysis", "typeVersion": 1.1, "position": [-96, -16], "id": "481c5544-0609-45bd-8c06-602a73936d72", "name": "user consent"}, {"parameters": {"public": true, "initialMessages": "=Hi there! 👋\nthank you for using \"this feature\" today. Would you like to help us improve by sharing your experience? It’ll only take 2 minutes.", "options": {"responseMode": "responseNodes"}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.3, "position": [-288, 0], "id": "d8c93559-3895-4357-8edf-e8b3f93f1fa6", "name": "user-chat", "webhookId": "f07f7a86-c957-49ac-b7bc-7203e3dafaf7"}, {"parameters": {"batchSize": 5, "options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [848, -144], "id": "59decaac-3af2-4b4d-9075-f0078af63cc2", "name": "Loop Over Items"}, {"parameters": {"message": "={{ $json.output }}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.chat", "typeVersion": 1, "position": [656, -256], "id": "604375ff-3a30-40d1-ba87-d19b4f476f77", "name": "Respond to Chat"}, {"parameters": {"description": "note: |\n    - **Approach**: Prioritize empathy and adaptability. If the user shows signs of frustration, validate their feelings before diving deeper.\n    - **Flexibility**: Adjust the flow based on the user’s responses, but always aim to uncover the root cause without pressuring them.\n    - **Objective**: Each interaction should yield actionable data for the product team while maintaining a positive experience for the user.\n    - **Confidentiality**: Assure the user that their responses are anonymous and will only be used for internal improvements.\n    - **Duration**: Keep the interview between 3-5 minutes unless the user expresses interest in extending it."}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1.1, "position": [560, -80], "id": "65009252-ad1c-40db-ade5-a14db8307e61", "name": "Think"}, {"parameters": {"toolDescription": "AI Agent that can call other tools to save the data from the user chat and interview", "text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Prompt__User_Message_', ``, 'string') }}", "options": {"systemMessage": "    - step: Closing and Appreciation\n      details:\n        - Summarize the user’s key points to confirm accurate capture of their feedback.\n        - Thank them for their time and reinforce the value of their contribution:\n          \"Thank you so much for sharing your experience, [Name]! Your feedback is invaluable for improving [feature]. If you have more ideas in the future, feel free to let us know.\"\n        - Offer an additional contact channel if they wish to contribute further.\n\n    - step: Documentation and Analysis\n      details:\n        - Record all responses, including:\n          - CSAT/CES ratings.\n          - Verbatim user comments.\n          - Context of the task completed and the feature evaluated.\n          - Observations about emotional tone (e.g., frustration, enthusiasm).\n        - Categorize insights by themes (usability, design, expectations, bugs, etc.) for further analysis.\n"}}, "type": "@n8n/n8n-nodes-langchain.agentTool", "typeVersion": 2.2, "position": [608, 144], "id": "a996fd20-e5bf-4d80-a14f-8a8e462a81ac", "name": "CSAT note taker"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [608, 304], "id": "8988789b-5daa-4740-9bb4-14e18bf5659b", "name": "Google Gemini Chat Model2", "credentials": {"googlePalmApi": {"id": "gz2xEfbZKFl5Ad7K", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [720, 368], "id": "072ab3b7-11ba-46b4-86c4-498608c7f72e", "name": "Simple Memory1"}], "pinData": {}, "connections": {"Google Gemini Chat Model": {"ai_languageModel": [[{"node": "user consent", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "user consent": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}], [{"node": "AI Agent", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "user-chat": {"main": [[{"node": "user consent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Respond to Chat", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "AI Agent", "type": "main", "index": 0}]]}, "Respond to Chat": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Think": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "CSAT note taker": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Google Gemini Chat Model2": {"ai_languageModel": [[{"node": "CSAT note taker", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory1": {"ai_memory": [[{"node": "CSAT note taker", "type": "ai_memory", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "50949289-7379-482c-97f9-790d94389a13", "meta": {"templateCredsSetupCompleted": true, "instanceId": "0828ed415eaadbb7067f7c2c63fc9888d18dab7916e2e53339b920682432ddca"}, "id": "ukKijZoKBM7YL9F4", "tags": [{"name": "ready", "id": "Wo6uHcS3wy5W6JFi", "createdAt": "2025-08-21T03:01:55.297Z", "updatedAt": "2025-08-21T03:01:55.297Z"}]}